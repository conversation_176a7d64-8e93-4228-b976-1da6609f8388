import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import {
  APHistoricRevenue,
  APHistoricRevenueBaseData,
} from "@/features/account-plan/types/revenue-types";
import { QUERY_KEYS } from "@/constants/query-keys";

export const createHistoricRevenue = ({
  accountId,
  data,
}: {
  accountId: number;
  data?: APHistoricRevenueBaseData;
}): ApiResponse<APHistoricRevenue> => {
  return api.post(API_ROUTES.ACCOUNT_PLANS_HISTORIC_REVENUE(accountId), data);
};

type UseCreateHistoricRevenueOptions = {
  mutationConfig?: MutationConfig<typeof createHistoricRevenue>;
};

export const useCreateHistoricRevenue = ({
  mutationConfig,
}: UseCreateHistoricRevenueOptions) => {
  const queryClient = useQueryClient();
  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      await queryClient.invalidateQueries({
        queryKey: [
          QUERY_KEYS.ACCOUNT_PLANS,
          args[1].accountId,
          QUERY_KEYS.ACCOUNT_PLANS_HISTORIC_REVENUE,
        ],
      });

      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: createHistoricRevenue,
  });
};
