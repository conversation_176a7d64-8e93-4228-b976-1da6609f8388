"use client";

import React, { useCallback } from "react";
import { useDropzone } from "react-dropzone";
import Image from "next/image";
import { X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { IconPhotoUp } from "@tabler/icons-react";
import { cn } from "@/lib/utils";

interface ImageUploaderProps {
  placeholder?: string;
  onUpload: (files: File[]) => void;
  previews?: string[];
  onRemovePreview?: (index: number) => void;
  className?: React.HTMLAttributes<HTMLDivElement>["className"];
}

export default function ImageUploader({
  onUpload,
  placeholder = "Drag 'n' drop some images here, or click to select files",
  previews,
  onRemovePreview,
  className,
}: ImageUploaderProps) {
  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      onUpload(acceptedFiles);
    },
    [onUpload]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "image/*": [],
    },
    multiple: true,
  });

  return (
    <div className={cn("w-fit", className)}>
      <div
        {...getRootProps()}
        className={`flex h-full w-full cursor-pointer flex-col items-center justify-center gap-res-y-sm rounded-lg border border-neutral-200 bg-white p-res-x-xl text-center text-neutral-400 transition-colors ${
          isDragActive
            ? "border-primary bg-primary/10"
            : "hover:border-primary border-gray-300"
        }`}
      >
        <input {...getInputProps()} />
        <IconPhotoUp className="size-[5vw]" />
        {isDragActive ? (
          <p className="text-primary">Drop the images here ...</p>
        ) : (
          <p>{placeholder}</p>
        )}
      </div>
      {(previews?.length ?? 0) > 0 && (
        <div className="mt-4 grid grid-cols-3 gap-4">
          {previews?.map((preview, index) => (
            <div key={index} className="relative">
              <Image
                src={preview}
                alt={`Preview ${index + 1}`}
                width={100}
                height={100}
                className="h-24 w-full rounded-md object-cover"
              />
              <Button
                variant="destructive"
                size="icon"
                className="absolute right-1 top-1 h-6 w-6"
                onClick={() => onRemovePreview?.(index)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
