import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { AccountPlanPayload, AccountPlanData } from "../types";
import { QUERY_KEYS } from "@/constants/query-keys";

export const updateAccountPlan = ({
  accountId,
  data,
}: {
  accountId: number;
  data?: AccountPlanPayload;
}): ApiResponse<AccountPlanData> => {
  return api.put(API_ROUTES.ACCOUNT_PLANS_DETAIL(accountId), data);
};

type UseUpdateAccountPlanOptions = {
  mutationConfig?: MutationConfig<typeof updateAccountPlan>;
};

export const useUpdateAccountPlan = ({
  mutationConfig,
}: UseUpdateAccountPlanOptions) => {
  const { onSuccess, invalidate, ...restConfig } = mutationConfig || {};
  const queryClient = useQueryClient();

  return useMutation({
    onSuccess: (...args) => {
      onSuccess?.(...args);

      if (invalidate) {
        queryClient.invalidateQueries({
          queryKey: [QUERY_KEYS.ACCOUNT_PLANS],
        });

        queryClient.invalidateQueries({
          queryKey: [
            QUERY_KEYS.ACCOUNT_PLANS_GROUPS,
            args[0].data.account_plan_group?.id,
          ],
        });
      }
    },
    ...restConfig,
    mutationFn: updateAccountPlan,
  });
};
