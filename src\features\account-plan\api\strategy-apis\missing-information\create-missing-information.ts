import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import {
  APMissingInformation,
  APMissingInformationBaseData,
} from "@/features/account-plan/types/strategy-types";
import { QUERY_KEYS } from "@/constants/query-keys";

export const createMissingInformation = ({
  accountId,
  data,
}: {
  accountId: number;
  data?: APMissingInformationBaseData;
}): ApiResponse<APMissingInformation> => {
  return api.post(
    API_ROUTES.ACCOUNT_PLANS_MISSING_INFORMATION(accountId),
    data
  );
};

type UseCreateMissingInformationOptions = {
  mutationConfig?: MutationConfig<typeof createMissingInformation>;
};

export const useCreateMissingInformation = ({
  mutationConfig,
}: UseCreateMissingInformationOptions) => {
  const queryClient = useQueryClient();
  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      await queryClient.invalidateQueries({
        queryKey: [
          QUERY_KEYS.ACCOUNT_PLANS,
          args[1].accountId,
          QUERY_KEYS.ACCOUNT_PLANS_MISSING_INFORMATION,
        ],
      });

      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: createMissingInformation,
  });
};
