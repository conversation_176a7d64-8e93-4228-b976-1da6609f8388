import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import {
  UserManagementInviteBaseData,
  UserManagementInviteData,
} from "../../types";

export const createUserManagementInvite = ({
  data,
}: {
  data?: UserManagementInviteBaseData;
}): ApiResponse<UserManagementInviteData> => {
  return api.post(`${API_ROUTES.USER_MANAGEMENTS_INVITE}`, data);
};

type UseCreateUserManagementInviteOptions = {
  mutationConfig?: MutationConfig<typeof createUserManagementInvite>;
};

export const useCreateUserManagementInvite = ({
  mutationConfig,
}: UseCreateUserManagementInviteOptions) => {
  const { onSuccess, ...restConfig } = mutationConfig || {};
  const queryClient = useQueryClient();

  return useMutation({
    onSuccess: (...args) => {
      onSuccess?.(...args);

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.USER_MANAGEMENTS],
      });
    },
    ...restConfig,
    mutationFn: createUserManagementInvite,
  });
};
