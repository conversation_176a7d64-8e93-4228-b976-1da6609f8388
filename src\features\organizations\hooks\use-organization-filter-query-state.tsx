import { parseAsString, useQueryState } from "next-usequerystate";

import { useAuth } from "@/features/auth/api/get-auth";

const useOrganizationFilterQueryState = () => {
  const { auth } = useAuth({});
  const organizationFilterQuery = useQueryState(
    "organization_id",
    parseAsString.withDefault(auth?.user.organization.id.toString() ?? "")
  );

  return organizationFilterQuery;
};

export default useOrganizationFilterQueryState;
