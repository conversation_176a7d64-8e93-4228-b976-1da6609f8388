import { cva, type VariantProps } from "class-variance-authority";
import * as React from "react";

import { cn } from "@/lib/utils";

const badgeVariants = cva(
  "inline-flex items-center justify-center rounded-full px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-neutral-950 focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border border-neutral-200 bg-neutral-900 text-neutral-50 hover:bg-neutral-900/80 dark:border-neutral-800 dark:bg-neutral-50 dark:text-neutral-900 dark:hover:bg-neutral-50/80 dark:focus:ring-neutral-300",
        secondary:
          "border border-neutral-200 bg-neutral-100 text-neutral-900 hover:bg-neutral-100/80 dark:border-neutral-800 dark:bg-neutral-800 dark:text-neutral-50 dark:hover:bg-neutral-800/80 dark:focus:ring-neutral-300",
        destructive:
          "border border-neutral-200 bg-red-500 text-neutral-50 hover:bg-red-500/80 dark:border-neutral-800 dark:bg-red-900 dark:text-neutral-50 dark:hover:bg-red-900/80 dark:focus:ring-neutral-300",
        outline: "text-neutral-950 dark:text-neutral-50",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div
      className={cn("text-base", badgeVariants({ variant }), className)}
      {...props}
    />
  );
}

export { Badge, badgeVariants };
