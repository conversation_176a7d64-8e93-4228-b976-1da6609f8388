import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { User, UserBaseParams } from "../../types/user";

type UserDetailParams = UserBaseParams;

export const getUser = ({
  userId,
  params,
}: {
  userId: number;
  params?: UserDetailParams;
}): ApiResponse<User> => {
  return api.get(API_ROUTES.USERS_DETAIL(userId), { params });
};

export const getUserQueryOptions = (
  userId: number,
  params?: UserDetailParams
) => {
  return queryOptions({
    queryKey: [QUERY_KEYS.USER, userId],
    queryFn: () => getUser({ userId, params }),
    enabled: !!userId,
  });
};

type UseUserOptions = {
  userId: number;
  params?: UserDetailParams;
  queryConfig?: QueryConfig<typeof getUser>;
  options?: Partial<ReturnType<typeof getUserQueryOptions>>;
};

export const useUser = ({
  userId,
  params,
  queryConfig,
  options,
}: UseUserOptions) => {
  const userQuery = useQuery({
    ...getUserQueryOptions(userId, params),
    ...queryConfig,
    ...options,
  });

  return {
    ...userQuery,
    user: userQuery.data?.data,
  };
};
