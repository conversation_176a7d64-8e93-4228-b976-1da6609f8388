import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import {
  APCurrentRevenue,
  APCurrentRevenueBaseData,
} from "@/features/account-plan/types/revenue-types";
import { QUERY_KEYS } from "@/constants/query-keys";

export const createCurrentRevenue = ({
  accountId,
  data,
}: {
  accountId: number;
  data?: APCurrentRevenueBaseData;
}): ApiResponse<APCurrentRevenue> => {
  return api.post(API_ROUTES.ACCOUNT_PLANS_CURRENT_REVENUE(accountId), data);
};

type UseCreateCurrentRevenueOptions = {
  mutationConfig?: MutationConfig<typeof createCurrentRevenue>;
};

export const useCreateCurrentRevenue = ({
  mutationConfig,
}: UseCreateCurrentRevenueOptions) => {
  const queryClient = useQueryClient();
  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      await queryClient.invalidateQueries({
        queryKey: [
          QUERY_KEYS.ACCOUNT_PLANS,
          args[1].accountId,
          QUERY_KEYS.ACCOUNT_PLANS_CURRENT_REVENUE,
        ],
      });

      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: createCurrentRevenue,
  });
};
