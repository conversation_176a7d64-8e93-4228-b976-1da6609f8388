"use client";

import React, { useRef } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";

import {
  Table,
  TableBody,
  TableCell,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { isRequestError } from "@/lib/api-client";
import { useRolesPermissions } from "@/features/user-management/hooks/use-role-permissions";
import { IconSettings, IconTrashXFilled } from "@tabler/icons-react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>rigger,
} from "@/components/ui/tooltip";
import { useOrganizationOptions } from "@/features/user-management/hooks/use-organization-list";

import { useCreateOrganization } from "../api/create-organization";
import { useDeleteOrganization } from "../api/delete-organization";
import { getOrganizationTierAlias } from "../constants";
import useOrganizationFilterQueryState from "../hooks/use-organization-filter-query-state";
import { OrganizationTier } from "../types";

const createOrganizationSchema = z.object({
  name: z.string().min(1, { message: "Please fill out the organization name" }),
  unique_id: z.string().optional(),
  designated_owner_email: z.string().email(),
  organization_identifier_id: z.string().optional(),
});

type CreateOrganizationForm = z.infer<typeof createOrganizationSchema>;

export function CreateOrganizationDialog() {
  const triggerRef = useRef<HTMLButtonElement | null>(null);
  const form = useForm<CreateOrganizationForm>({
    resolver: zodResolver(createOrganizationSchema),
    reValidateMode: "onSubmit",
    defaultValues: {
      name: "",
      unique_id: "",
      designated_owner_email: "",
    },
  });

  const createOrganization = useCreateOrganization({});

  const onCreateNewAccountPlanGroup = async (
    values: CreateOrganizationForm
  ) => {
    const { unique_id, ...data } = values;

    try {
      await createOrganization.mutateAsync({
        data: {
          ...data,
          ...(!!unique_id && { unique_id }),
        },
      });

      triggerRef.current?.click();
    } catch (e) {
      if (isRequestError(e)) {
        toast.error(e.response?.data.errors[0].message);
      } else {
        toast.error("An error occured while creating new account plan");
      }
    }
  };

  return (
    <Dialog>
      <DialogTrigger asChild ref={triggerRef}>
        <Button variant="secondary">+ Add Organization</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogTitle>Create new organization</DialogTitle>
        <DialogDescription>
          Enter the details of new organization
        </DialogDescription>

        <Form {...form}>
          <form className="mt-res-y-base flex flex-col gap-res-y-base">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center">
                    Organization name
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Input the organization name"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="designated_owner_email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel htmlFor="email" className="flex items-center">
                    Owner Email
                  </FormLabel>
                  <FormControl>
                    <Input
                      id="email"
                      type="email"
                      placeholder="Input the owner email address"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="unique_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center">
                    Unique Organization ID (Optional)
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter the unique organization ID"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="organization_identifier_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center">
                    Employee ID (Optional)
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="Enter the employee ID" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button
              className="mt-res-y-base"
              type="submit"
              onClick={form.handleSubmit(onCreateNewAccountPlanGroup)}
              isLoading={createOrganization.isPending}
            >
              Create organization
            </Button>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

export const OrganizationListTable = () => {
  const { organizationOptions } = useOrganizationOptions();
  const permissions = useRolesPermissions();
  const [organizationFilter, setOrganizationFilter] =
    useOrganizationFilterQueryState();

  const deleteOrganization = useDeleteOrganization({
    mutationConfig: {
      invalidate: true,
    },
  });

  const onDeleteOrganization = async (organizationId?: number) => {
    try {
      if (!organizationId) {
        throw new Error();
      }

      await deleteOrganization.mutateAsync({
        organizationId,
      });

      if (organizationId === Number(organizationFilter)) {
        setOrganizationFilter(organizationOptions[0].id.toString());
      }

      toast("Organization has been removed");
    } catch (_) {
      toast.error("An error occured upon organization deletion");
    }
  };

  if (!permissions.isAbleToManageOtherOrganizations) return null;

  return (
    <Dialog>
      <DialogTrigger>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <Button variant="icon" size="icon">
                <IconSettings />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Manage Organization</TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </DialogTrigger>
      <DialogContent>
        <div className="mt-res-y-base">
          <div className="mb-res-y-lg flex items-center justify-between">
            <p className="text-2xl font-bold text-primary-500">
              Manage Organization
            </p>
            <CreateOrganizationDialog />
          </div>
          <div className="max-h-[85vh] overflow-auto rounded border">
            <Table>
              <TableHeader>
                <TableCell width="25%">Organization ID</TableCell>
                <TableCell>Organization Name</TableCell>
                <TableCell>Tier</TableCell>
                <TableCell></TableCell>
              </TableHeader>
              <TableBody>
                {organizationOptions.map((organization, idx) => (
                  <TableRow key={idx}>
                    <TableCell>{organization.unique_id}</TableCell>
                    <TableCell>{organization.label}</TableCell>
                    <TableCell>
                      {getOrganizationTierAlias(organization.tier)}
                    </TableCell>
                    <TableCell>
                      {organization.tier !== OrganizationTier.SUPERUSER && (
                        <Button variant="icon" size="icon">
                          <IconTrashXFilled
                            className="size-res-y-lg text-red-500"
                            onClick={() =>
                              onDeleteOrganization(organization.id)
                            }
                          />
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
