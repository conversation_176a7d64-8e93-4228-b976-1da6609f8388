import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { OrganizationPayload, OrganizationData } from "../types";

export const updateOrganization = ({
  organizationId,
  data,
}: {
  organizationId: number;
  data?: OrganizationPayload;
}): ApiResponse<OrganizationData> => {
  return api.put(API_ROUTES.ORGANIZATIONS_DETAIL(organizationId), data);
};

type UseUpdateOrganizationOptions = {
  mutationConfig?: MutationConfig<typeof updateOrganization>;
};

export const useUpdateOrganization = ({
  mutationConfig,
}: UseUpdateOrganizationOptions) => {
  const { onSuccess, invalidate, ...restConfig } = mutationConfig || {};
  const queryClient = useQueryClient();

  return useMutation({
    onSuccess: (...args) => {
      onSuccess?.(...args);

      if (invalidate) {
        queryClient.invalidateQueries({
          queryKey: [QUERY_KEYS.ORGANIZATION],
        });
      }
    },
    ...restConfig,
    mutationFn: updateOrganization,
  });
};
