import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { api } from "@/lib/api-client";
import { ApiResponse, QueryConfig } from "@/lib/react-query";
import { queryOptions, useQuery } from "@tanstack/react-query";

type CrmContact = {
  id: number;
  name: string;
  job_title: string;
  location: string;
};

type CrmContactParams = {
  crm_type: "hubspot" | "salesforce";
};

export const getCrmContacts = async ({
  accountId,
  params,
}: {
  accountId: number;
  params: CrmContactParams;
}): ApiResponse<CrmContact[]> =>
  api.get(API_ROUTES.CRM_CONTACTS(accountId), { params });

export const getCrmContactsQueryOptions = (
  accountId: number,
  params: CrmContactParams
) => {
  return queryOptions({
    queryKey: [
      QUERY_KEYS.ACCOUNT_PLANS,
      accountId,
      "crm-contacts",
      params.crm_type,
    ],
    queryFn: () => getCrmContacts({ accountId, params }),
    enabled: !!accountId,
  });
};

type UseCrmContactsOptions = {
  params: CrmContactParams;
  accountId: number;
  queryConfig?: QueryConfig<typeof getCrmContacts>;
  options?: Partial<ReturnType<typeof getCrmContactsQueryOptions>>;
};

export const useCrmContacts = ({
  accountId,
  params,
  queryConfig,
  options,
}: UseCrmContactsOptions) => {
  const crmContactsQuery = useQuery({
    ...getCrmContactsQueryOptions(accountId, params),
    ...queryConfig,
    ...options,
  });

  return {
    ...crmContactsQuery,
    crmContacts: crmContactsQuery.data?.data,
  };
};
