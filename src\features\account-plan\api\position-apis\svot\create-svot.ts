import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import {
  APSvot,
  APSvotBaseData,
} from "@/features/account-plan/types/position-types";
import { QUERY_KEYS } from "@/constants/query-keys";

export const createSvot = ({
  accountId,
  data,
}: {
  accountId: number;
  data?: APSvotBaseData;
}): ApiResponse<APSvot> => {
  return api.post(API_ROUTES.ACCOUNT_PLANS_SVOT(accountId), data);
};

type UseCreateSvotOptions = {
  mutationConfig?: MutationConfig<typeof createSvot>;
};

export const useCreateSvot = ({ mutationConfig }: UseCreateSvotOptions) => {
  const queryClient = useQueryClient();
  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      await queryClient.invalidateQueries({
        queryKey: [
          QUERY_KEYS.ACCOUNT_PLANS,
          args[1].accountId,
          QUERY_KEYS.ACCOUNT_PLANS_SVOT,
        ],
      });

      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: createSvot,
  });
};
