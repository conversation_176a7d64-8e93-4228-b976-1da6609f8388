import { queryOptions, useQueries, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, BaseParams, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { APPotentialOpportunity } from "@/features/account-plan/types/revenue-types";

type PotentialOpportunityListParams = BaseParams;

export const getPotentialOpportunityList = ({
  accountId,
  params,
}: {
  accountId: number;
  params?: PotentialOpportunityListParams;
}): ApiResponse<APPotentialOpportunity[]> => {
  return api.get(API_ROUTES.ACCOUNT_PLANS_POTENTIAL_OPPORTUNITY(accountId), {
    params,
  });
};

export const getPotentialOpportunityListQueryOptions = (
  accountId: number,
  params?: PotentialOpportunityListParams
) => {
  return queryOptions({
    queryKey: [
      QUERY_KEYS.ACCOUNT_PLANS,
      accountId,
      QUERY_KEYS.ACCOUNT_PLANS_POTENTIAL_OPPORTUNITY,
    ],
    queryFn: () => getPotentialOpportunityList({ accountId, params }),
    enabled: !!accountId,
  });
};

type UsePotentialOpportunityListOptions = {
  params?: PotentialOpportunityListParams;
  accountId: number;
  queryConfig?: QueryConfig<typeof getPotentialOpportunityList>;
  options?: Partial<ReturnType<typeof getPotentialOpportunityListQueryOptions>>;
};

export const usePotentialOpportunityList = ({
  params,
  accountId,
  queryConfig,
  options,
}: UsePotentialOpportunityListOptions) => {
  const potentialOpportunityListQuery = useQuery({
    ...getPotentialOpportunityListQueryOptions(accountId, params),
    ...queryConfig,
    ...options,
  });

  return {
    ...potentialOpportunityListQuery,
    potentialOpportunityList: potentialOpportunityListQuery.data?.data,
  };
};

type UsePotentialOpportunityListsOptions = {
  queries: {
    accountId: number;
    params?: PotentialOpportunityListParams;
  }[];
  queryConfig?: QueryConfig<typeof getPotentialOpportunityList>;
  options?: Partial<ReturnType<typeof getPotentialOpportunityListQueryOptions>>;
};

export const usePotentialOpportunityLists = ({
  queries,
  queryConfig,
}: UsePotentialOpportunityListsOptions) => {
  const results = useQueries({
    queries: queries.map(({ accountId, params }) => ({
      ...getPotentialOpportunityListQueryOptions(accountId, params),
      ...queryConfig,
    })),
  });

  return results.map((result, idx) => ({
    ...result,
    query: queries[idx],
    potentialOpportunityList: result.data?.data ?? [],
  }));
};
