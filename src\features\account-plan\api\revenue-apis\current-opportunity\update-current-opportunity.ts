import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import {
  APCurrentOpportunity,
  APCurrentOpportunityBaseData,
} from "@/features/account-plan/types/revenue-types";

export const updateCurrentOpportunity = ({
  accountId,
  id,
  data,
}: {
  id: number;
  accountId: number;
  data?: APCurrentOpportunityBaseData;
}): ApiResponse<APCurrentOpportunity> => {
  return api.put(
    API_ROUTES.ACCOUNT_PLANS_CURRENT_OPPORTUNITY_DETAIL(accountId, id),
    data
  );
};

type UseUpdateCurrentOpportunityOptions = {
  mutationConfig?: MutationConfig<typeof updateCurrentOpportunity>;
};

export const useUpdateCurrentOpportunity = ({
  mutationConfig,
}: UseUpdateCurrentOpportunityOptions) => {
  const queryClient = useQueryClient();
  const { onSuccess, invalidate, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      if (invalidate) {
        await queryClient.invalidateQueries({
          queryKey: [
            QUERY_KEYS.ACCOUNT_PLANS,
            args[1].accountId,
            QUERY_KEYS.ACCOUNT_PLANS_CURRENT_OPPORTUNITY,
          ],
        });
      }

      await queryClient.invalidateQueries({
        queryKey: [
          QUERY_KEYS.ACCOUNT_PLANS,
          args[1].accountId,
          QUERY_KEYS.ACCOUNT_PLANS_REVENUE_FORECAST,
        ],
      });

      onSuccess?.(...args);
    },

    ...restConfig,
    mutationFn: updateCurrentOpportunity,
  });
};
