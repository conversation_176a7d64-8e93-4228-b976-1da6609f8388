import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import {
  APTopAction,
  APTopActionBaseData,
} from "@/features/account-plan/types/strategy-types";
import { QUERY_KEYS } from "@/constants/query-keys";

export const createTopAction = ({
  accountId,
  data,
}: {
  accountId: number;
  data?: APTopActionBaseData;
}): ApiResponse<APTopAction> => {
  return api.post(API_ROUTES.ACCOUNT_PLANS_TOP_ACTION(accountId), data);
};

type UseCreateTopActionOptions = {
  mutationConfig?: MutationConfig<typeof createTopAction>;
};

export const useCreateTopAction = ({
  mutationConfig,
}: UseCreateTopActionOptions) => {
  const queryClient = useQueryClient();
  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      await queryClient.invalidateQueries({
        queryKey: [
          QUERY_KEYS.ACCOUNT_PLANS,
          args[1].accountId,
          QUERY_KEYS.ACCOUNT_PLANS_TOP_ACTION,
        ],
      });

      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: createTopAction,
  });
};
