import { useMutation } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";

type UploadData = {
  file: File;
};

export type Presigned = {
  data: {
    presigned_url: string;
    path: string;
    download_url: string;
    filename: string;
  };
};

export const uploadWithPresigned = async ({ data }: { data: UploadData }) => {
  const presigned: Presigned = await api.get(API_ROUTES.S3_UPLOAD, {
    params: {
      filename: data.file.name,
      extension: data.file.name.split(".").pop(),
      directory: data.file.type.includes("image/") ? "images" : "files",
      acl: "public-read",
    },
  });

  await fetch(presigned.data.presigned_url, {
    method: "PUT",
    headers: {
      "Content-Type": data.file.type,
    },
    body: data.file,
  });

  return presigned;
};

type UseUploadOptions = {
  mutationConfig?: MutationConfig<typeof uploadWithPresigned>;
};

export const useUploadData = ({ mutationConfig }: UseUploadOptions) => {
  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: uploadWithPresigned,
  });
};
