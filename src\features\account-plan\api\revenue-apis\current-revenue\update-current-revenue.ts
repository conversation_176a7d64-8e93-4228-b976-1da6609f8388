import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import {
  APCurrentRevenue,
  APCurrentRevenueBaseData,
} from "@/features/account-plan/types/revenue-types";

export const updateCurrentRevenue = ({
  accountId,
  id,
  data,
}: {
  id: number;
  accountId: number;
  data?: APCurrentRevenueBaseData;
}): ApiResponse<APCurrentRevenue> => {
  return api.put(
    API_ROUTES.ACCOUNT_PLANS_CURRENT_REVENUE_DETAIL(accountId, id),
    data
  );
};

type UseUpdateCurrentRevenueOptions = {
  mutationConfig?: MutationConfig<typeof updateCurrentRevenue>;
};

export const useUpdateCurrentRevenue = ({
  mutationConfig,
}: UseUpdateCurrentRevenueOptions) => {
  const queryClient = useQueryClient();
  const { onSuccess, invalidate, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      if (invalidate) {
        await queryClient.invalidateQueries({
          queryKey: [
            QUERY_KEYS.ACCOUNT_PLANS,
            args[1].accountId,
            QUERY_KEYS.ACCOUNT_PLANS_CURRENT_REVENUE,
          ],
        });
      }

      await queryClient.invalidateQueries({
        queryKey: [
          QUERY_KEYS.ACCOUNT_PLANS,
          args[1].accountId,
          QUERY_KEYS.ACCOUNT_PLANS_REVENUE_FORECAST,
        ],
      });

      onSuccess?.(...args);
    },

    ...restConfig,
    mutationFn: updateCurrentRevenue,
  });
};
