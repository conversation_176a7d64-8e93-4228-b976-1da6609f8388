"use client";

import React, {
  cloneElement,
  isValidElement,
  ReactNode,
  useEffect,
  useState,
} from "react";

import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogTrigger,
} from "@/components/ui/dialog";
import { cn } from "@/lib/utils";

import { useScalingDimension } from "@/lib/hooks/use-scaling-dimension";
import { Grid, GridProps, GridRef } from "@/components/ui/grid";
import { Button } from "@/components/ui/button";
import DataTable from "@/components/ui/data-table";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

import { useAccountPlanInput } from ".";
import { AccountPlanTableType } from "../../types";
import { useAccountPlanStore } from "../../stores";
import useUpdateReviewDate from "./hooks/use-update-review-date";
import { getAccountPlanTableName } from "../../constants";

export const AccountPlanTableTooltip = {
  stakeholder_mapping:
    "List ALL stakeholders who have any level of influence over prioritization and decision-making with you and your competitors within the AAA, irrespective of location or ease of access to them. (Partial information is better than none, you can find additional information as you progress).",
  wallet_share:
    "Define the addressable wallet size, your share and that of your competitors, to establish what could be available. Record the services and products provided by you and your competitors.",
  circumstantial_analysis:
    "Analyze and understand the news, trends and events that are influencing the account’s prioritization and decision-making. Understand what is happening at the Macro, Industry, and Business level that informs us about potential opportunities to help move the relationship and revenue forward.",
  svot: "From the Circumstantial Analysis and your knowledge of this AAA, identify your Strengths, Vulnerabilities, Opportunities and Threats that could impact upon the growth of the relationship and revenue.",
  insight_and_perspective: (
    <>
      Identify opportunities to add value:
      <ul className="list-disc pl-res-x-sm">
        <li>
          <b>Know them well</b> – Understand their business and demonstrate that
          knowledge to a high level.
        </li>
        <li>
          <b>Make good use of their time</b> - Facilitate mutual and compelling
          conversations that respect their time and abilities.
        </li>
        <li>
          <b>Be relevant and pro-active</b> - Focus on solving their issues over
          selling products/services, help them see future issues and demonstrate
          innovative thinking.
        </li>
        <li>
          <b>Educate them</b> - Differentiate by sharing insights that help them
          make decisions, develop their understanding, and expand their
          expertise.
        </li>
      </ul>
    </>
  ),
  historic_revenue:
    "What are the historic services and products bought and associated revenue generated by this AAA?",
  current_revenue:
    "What are the existing services and products being bought and associated revenue generated by this AAA?",
  current_opportunity:
    "What sales opportunities are you currently pursuing with the client, irrespective of the sales stage (exclude opportunities that are either closed-lost or closed-won)?",
  potential_opportunity:
    "List ALL possible cross/up-sell opportunities that could be pursued, even if they are vague and not yet developed.",
  revenue_forecast:
    "Your revenue forecast is based on the analysis of the information within this account plan, calculated by adding Existing Revenue + Opportunities In Play + Cross/Up-Sell Opportunities.",
  missing_information:
    "Any information you think is currently missing, which would add to your understanding of your client and its stakeholders, to strengthen your strategic analysis.",
  targeted_perception_development: (
    <>
      Select one or two strategic stakeholders, who’s improved perception can
      lift your overall relationship with the client. Follow these 4 steps:
      <ul className="list-outside list-disc pl-res-x-base">
        <li>
          Identify a stakeholder within the AAA who is listed in your
          Stakeholder Mapping.
        </li>
        <li>Identify an issue important to them you can solve.</li>
        <li>
          Think through what strengths you can leverage to help them solve the
          issue and achieve the result they need.
        </li>
        <li>Follow the scripted component of the plan.</li>
      </ul>
    </>
  ),
  action_plan:
    "This account planning tool provides you with a series of Strategic Considerations generated by the AI engine. Consider these when formulating your Strategic Action Plan.",
  top_action:
    "Take into consideration all the information in your account plan as well as the Strategic Considerations. Create a list of prioritized actions you will complete between now and your next plan review. Update your plan when you have completed each strategic action.",
  client_meeting_schedule:
    "A schedule of upcoming meetings with stakeholders at the client and notes on what should be discussed.",
};

export type AccountTableProps = {
  isPreview?: boolean;
  type: AccountPlanTableType;
  heightRatio?: number;
  footer?: React.ReactNode;
  onClose?: () => void;
  onGenerate?: () => void;
  isLoading?: boolean;
};

export const AccountTable = ({
  className,
  children,
  type,
  heightRatio = 0.2,
  footer = null,
  onClose,
  onGenerate,
  isLoading,

  ...props
}: React.HTMLAttributes<HTMLTableElement> & AccountTableProps) => {
  const { containerWidth } = useAccountPlanInput();
  const { getHeight, windowHeight, windowWidth } = useScalingDimension();
  const { onUpdateReviewDate } = useUpdateReviewDate();

  const activeTable = useAccountPlanStore((state) => state.activeTable);
  const toggleActiveTable = useAccountPlanStore(
    (state) => state.toggleActiveTable
  );

  const [tableHeight, setTableHeight] = useState<number>(0);

  useEffect(() => {
    const calculateTableHeight = () => {
      const paddingYHeight = windowHeight * 0.08;
      const topMenuHeight = windowHeight * 0.06;
      const groupMenuTitleHeight = 28 + 0.02 * windowHeight;
      const paddingBottom = windowHeight * 0.025;

      const offsetHeight =
        paddingYHeight + topMenuHeight + groupMenuTitleHeight + paddingBottom;

      setTableHeight((window.innerHeight - offsetHeight) * heightRatio);
    };

    calculateTableHeight();

    window.addEventListener("resize", calculateTableHeight);

    return () => {
      window.removeEventListener("resize", calculateTableHeight);
    };
  }, [getHeight, windowHeight, heightRatio]);

  if (!containerWidth) return null;

  const GRID_COL_COUNT = 3;
  const GRID_COL_GAP = 0.015 * windowWidth * (GRID_COL_COUNT - 1);

  const previewWidth = (containerWidth - GRID_COL_GAP) / GRID_COL_COUNT;
  const scale = previewWidth / containerWidth;

  const triggerChildren = React.Children.map(children, (child) => {
    const titleHeight = windowHeight * 0.06;
    const originalTableHeight = tableHeight / scale;

    if (
      isValidElement(child) &&
      (child.type === AccountTableGrid || child.type === DataTable)
    ) {
      return cloneElement(child, {
        //@ts-ignore
        height: `${originalTableHeight - titleHeight}px`,
        isPreview: true,
      });
    }

    if (isValidElement(child) && child.type === AccountTableTitle) {
      return cloneElement(child, {
        //@ts-ignore
        isPreview: true,
      });
    }

    return child;
  });

  return (
    <Dialog
      open={activeTable === type}
      onOpenChange={(isOpen) => {
        if (!isOpen) {
          onClose?.();
          onUpdateReviewDate();
        }

        toggleActiveTable(type);
      }}
    >
      <DialogTrigger asChild>
        <div
          className={cn(
            "cursor-pointer border",
            "overflow-hidden rounded-lg shadow-md hover:overflow-y-auto",
            "[&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-primary-400 [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar]:w-2",
            className
          )}
          style={{ height: `${tableHeight}px` }}
        >
          <AccountTableTitle
            tooltip={AccountPlanTableTooltip[type]}
            className="h-6 rounded text-xs"
            onGenerate={onGenerate}
            isLoading={isLoading}
            isPreview={true}
          >
            {getAccountPlanTableName(type)}
          </AccountTableTitle>

          <div
            style={{ width: `${containerWidth - 58}px`, scale }}
            className={cn(`!max-h-[100px] origin-top-left`)}
            {...props}
          >
            {triggerChildren}
          </div>
        </div>
      </DialogTrigger>
      <DialogContent className="!max-h-[85%] !max-w-[90%] overflow-auto p-4">
        <div
          className={cn("w-full caption-bottom text-sm", className)}
          {...props}
        >
          <DialogClose />
          <AccountTableTitle
            tooltip={AccountPlanTableTooltip[type]}
            onGenerate={onGenerate}
            isLoading={isLoading}
          >
            {getAccountPlanTableName(type)}
          </AccountTableTitle>
          {children}
          {!!footer && <AccountTableFooter>{footer}</AccountTableFooter>}
        </div>
      </DialogContent>
    </Dialog>
  );
};

const AccountTableFooter = ({ children }: { children: React.ReactNode }) => {
  return <div className="mt-4 flex justify-end gap-4 pt-4">{children}</div>;
};

export const AccountTableTitle = ({
  children,
  tooltip,
  variant = "primary",
  isLoading,
  onGenerate,
  isPreview,
  className,
}: {
  children: ReactNode;
  tooltip?: ReactNode;
  variant?: "primary" | "secondary";
  isLoading?: boolean;
  onGenerate?: () => void;
  isPreview?: boolean;
  className?: string;
}) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div
      className={cn(
        "relative sticky top-0 z-20",
        "flex h-[50px] items-center justify-center rounded-t-xl p-2 text-center text-4xl font-semibold text-white",
        variant === "primary" ? "bg-gradient" : "bg-secondary-500",
        className
      )}
    >
      {!!tooltip ? (
        <Popover open={isOpen}>
          <PopoverTrigger
            onMouseLeave={() => setIsOpen(false)}
            onMouseEnter={() => setIsOpen(true)}
          >
            {children}
          </PopoverTrigger>
          <PopoverContent
            className={cn(
              "flex-wrap whitespace-normal px-res-x-sm py-res-y-sm text-left font-semibold",
              isPreview ? "w-[30vw]" : "w-[40vw] text-lg"
            )}
            side="bottom"
            align="start"
          >
            {tooltip}
          </PopoverContent>
        </Popover>
      ) : (
        children
      )}
      {!!onGenerate && (
        <Button
          variant={variant === "primary" ? "secondary" : "default"}
          size={isPreview ? "sm" : "lg"}
          className={cn(
            "absolute right-2",
            isPreview ? "h-4 min-h-4 px-1 py-0 text-xs" : "text-xl"
          )}
          isLoading={isLoading}
          onClick={(e) => {
            e.stopPropagation();
            onGenerate();
          }}
        >
          {isPreview ? "Generate" : "Generate Analysis"}
        </Button>
      )}
    </div>
  );
};

export const AccountTableGrid = <T extends {}>(
  props: GridProps<T> & { ref?: GridRef<T>; disableNoSelectionStyle?: boolean }
) => {
  const { className, disableNoSelectionStyle, ...rest } = props;

  const { getHeight } = useScalingDimension();

  return (
    <Grid<T>
      variant="alt1"
      className={cn(
        !props.rowSelection &&
          !disableNoSelectionStyle &&
          "[&_.ag-column-first]:!pl-res-x-2xl",
        className
      )}
      headerHeight={getHeight(80)}
      rowHeight={getHeight(80)}
      {...rest}
    />
  );
};
