import { UserRole } from "@/features/auth/types/user";

export const rolesList = [
  {
    name: "Super Admin",
    value: UserRole.OWNER,
  },
  {
    name: "Organization Admin",
    value: UserRole.SUPER_ADMIN,
  },
  {
    name: "Organization User",
    value: UserRole.MEMBER,
  },
];

export const getRoleAlias = (role?: UserRole) => {
  return rolesList.find((v) => v.value === role)?.name ?? "-";
};
