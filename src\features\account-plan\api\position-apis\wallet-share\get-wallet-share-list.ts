import { queryOptions, useQueries, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, BaseParams, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { APWalletShare } from "@/features/account-plan/types/position-types";

type WalletShareListParams = BaseParams;

export const getWalletShareList = ({
  accountId,
}: {
  accountId: number;
  params?: WalletShareListParams;
}): ApiResponse<APWalletShare[]> => {
  return api.get(API_ROUTES.ACCOUNT_PLANS_WALLET_SHARE(accountId));
};

export const getWalletShareListQueryOptions = (
  accountId: number,
  params?: WalletShareListParams
) => {
  return queryOptions({
    queryKey: [
      QUERY_KEYS.ACCOUNT_PLANS,
      accountId,
      QUERY_KEYS.ACCOUNT_PLANS_WALLET_SHARE,
    ],
    queryFn: () => getWalletShareList({ accountId, params }),
    enabled: !!accountId,
  });
};

type UseWalletShareListOptions = {
  params?: WalletShareListParams;
  accountId: number;
  queryConfig?: QueryConfig<typeof getWalletShareList>;
  options?: Partial<ReturnType<typeof getWalletShareListQueryOptions>>;
};

export const useWalletShareList = ({
  params,
  accountId,
  queryConfig,
  options,
}: UseWalletShareListOptions) => {
  const walletShareListQuery = useQuery({
    ...getWalletShareListQueryOptions(accountId, params),
    ...queryConfig,
    ...options,
  });

  return {
    ...walletShareListQuery,
    walletShareList: walletShareListQuery.data?.data,
  };
};

type UseWalletShareListsOptions = {
  queries: {
    accountId: number;
    params?: WalletShareListParams;
  }[];
  queryConfig?: QueryConfig<typeof getWalletShareList>;
  options?: Partial<ReturnType<typeof getWalletShareListQueryOptions>>;
};

export const useWalletShareLists = ({
  queries,
  queryConfig,
}: UseWalletShareListsOptions) => {
  const results = useQueries({
    queries: queries.map(({ accountId, params }) => ({
      ...getWalletShareListQueryOptions(accountId, params),
      ...queryConfig,
    })),
  });

  return results.map((result, idx) => ({
    ...result,
    query: queries[idx],
    walletShareList: result.data?.data ?? [],
  }));
};
