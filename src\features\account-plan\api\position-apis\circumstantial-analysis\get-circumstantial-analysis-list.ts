import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, BaseParams, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { APCircumstantialAnalysis } from "@/features/account-plan/types/position-types";

type CircumstantialAnalysisListParams = BaseParams;

export const getCircumstantialAnalysisList = ({
  accountId,
}: {
  accountId: number;
  params?: CircumstantialAnalysisListParams;
}): ApiResponse<APCircumstantialAnalysis[]> => {
  return api.get(API_ROUTES.ACCOUNT_PLANS_CIRCUMSTANTIAL_ANALYSIS(accountId));
};

export const getCircumstantialAnalysisListQueryOptions = (
  accountId: number,
  params?: CircumstantialAnalysisListParams
) => {
  return queryOptions({
    queryKey: [
      QUERY_KEYS.ACCOUNT_PLANS,
      accountId,
      QUERY_KEYS.ACCOUNT_PLANS_CIRCUMSTANTIAL_ANALYSIS,
    ],
    queryFn: () => getCircumstantialAnalysisList({ accountId, params }),
    enabled: !!accountId,
  });
};

type UseCircumstantialAnalysisListOptions = {
  params?: CircumstantialAnalysisListParams;
  accountId: number;
  queryConfig?: QueryConfig<typeof getCircumstantialAnalysisList>;
  options?: Partial<
    ReturnType<typeof getCircumstantialAnalysisListQueryOptions>
  >;
};

export const useCircumstantialAnalysisList = ({
  params,
  accountId,
  queryConfig,
  options,
}: UseCircumstantialAnalysisListOptions) => {
  const circumstantialAnalysisListQuery = useQuery({
    ...getCircumstantialAnalysisListQueryOptions(accountId, params),
    ...queryConfig,
    ...options,
  });

  return {
    ...circumstantialAnalysisListQuery,
    circumstantialAnalysisList: circumstantialAnalysisListQuery.data?.data,
  };
};
