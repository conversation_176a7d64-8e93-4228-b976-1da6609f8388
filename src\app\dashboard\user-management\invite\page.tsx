"use client";

import React, { useEffect, useState } from "react";
import { useForm, useWatch } from "react-hook-form";
import { z } from "zod";
import { toast } from "sonner";
import Link from "next/link";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { But<PERSON> } from "@/components/ui/button";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { isRequestError } from "@/lib/api-client";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { IconAlertCircle } from "@tabler/icons-react";
import { useCreateUserManagementInvite } from "@/features/user-management/api/invitations/create-user-management-invite";
import { PATH } from "@/constants/path";
import { UserRole } from "@/features/auth/types/user";
import { useRolesPermissions } from "@/features/user-management/hooks/use-role-permissions";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useAuth } from "@/features/auth/api/get-auth";
import { useIsHydrated } from "@/lib/hooks/use-hydrated";
import { useOrganizationOptions } from "@/features/user-management/hooks/use-organization-list";

const formSchema = z.object({
  email: z
    .string({ message: "Please enter a valid email address" })
    .email({ message: "Please enter a valid email address" }),
  organization_id: z.number().optional(),
  role: z.nativeEnum(UserRole, { message: "Please select a role" }),
  organization_identifier_id: z.string().optional(),
});

type FormSchema = z.infer<typeof formSchema>;

function InvitePage() {
  const form = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    reValidateMode: "onSubmit",
    defaultValues: {
      email: "",
    },
  });
  const roleValue = useWatch({
    control: form.control,
    name: "role",
  });

  const createUserManagementInvite = useCreateUserManagementInvite({});
  const { assignableUserRolesList, isAbleToManageOtherOrganizations } =
    useRolesPermissions();
  const { isHydrated } = useIsHydrated();
  const { auth } = useAuth({});
  const { organizationOptions } = useOrganizationOptions();

  const [errorNotif, setErrorNotif] = useState<string>("");

  const onSubmit = async (values: FormSchema) => {
    try {
      await createUserManagementInvite.mutateAsync({ data: values });

      toast(`Invitation has been sent to ${values.email}`);
      setErrorNotif("");

      form.resetField("email");
      form.resetField("organization_identifier_id");
    } catch (e) {
      if (isRequestError(e)) {
        const errorMessage = e.response?.data.errors[0].message ?? "";
        setErrorNotif(errorMessage);
      }
    }
  };

  useEffect(() => {
    if (!!auth) {
      form.setValue("organization_id", auth?.user.organization.id);
    }
  }, [auth, form]);

  if (!isHydrated) return null;

  return (
    <div className="w-full">
      <div className="mb-8 flex justify-between">
        <div className="grid gap-2">
          <h1 className="text-3xl font-bold text-primary-500">
            Invite to Team
          </h1>
        </div>
      </div>
      <section className="mt-8 rounded-md bg-white p-8">
        <Form {...form}>
          <form
            className="grid w-[30vw] gap-8"
            onSubmit={form.handleSubmit(onSubmit)}
          >
            {errorNotif && (
              <Alert variant="destructive" className="relative w-full">
                <IconAlertCircle className="h-4 w-4" />
                <AlertTitle className="font-bold">
                  Send Invitation Failed
                </AlertTitle>
                <AlertDescription>{errorNotif}</AlertDescription>
              </Alert>
            )}
            {isAbleToManageOtherOrganizations && (
              <FormField
                control={form.control}
                name="organization_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel
                      htmlFor="organization_id"
                      className="text-lg font-semibold"
                    >
                      Organization
                    </FormLabel>
                    <p className="text-xs text-neutral-500">
                      Select an organization to assign to the new user.
                    </p>
                    <FormControl>
                      <Select
                        onValueChange={(value: string) => {
                          field.onChange(Number(value));
                        }}
                        value={field.value?.toString() ?? ""}
                        disabled={roleValue === UserRole.OWNER}
                      >
                        <SelectTrigger className="mb-res-x-sm w-[15vw]">
                          <SelectValue placeholder="Select organization" />
                        </SelectTrigger>
                        <SelectContent>
                          {organizationOptions.map((v, idx) => (
                            <SelectItem key={idx} value={v.value ?? ""}>
                              {v.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem className="w-[20vw]">
                  <FormLabel htmlFor="email" className="text-lg font-semibold">
                    Email
                  </FormLabel>
                  <p className="text-xs text-neutral-500">
                    Enter the email address of the user you would like to invite
                  </p>
                  <FormControl>
                    <Input
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      className="w-full"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="role"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-lg font-semibold">
                    Select a role
                  </FormLabel>
                  <p className="mb-4 text-xs text-neutral-500">
                    Choose the role you want to assign to the users
                  </p>
                  <FormControl>
                    <RadioGroup
                      onValueChange={(role) => {
                        if (role === UserRole.OWNER) {
                          form.setValue(
                            "organization_id",
                            auth?.user.organization.id
                          );
                        }

                        field.onChange(role);
                      }}
                      defaultValue={field.value}
                      className="!mt-4 flex flex-col gap-4"
                    >
                      {assignableUserRolesList.map((v, idx) => (
                        <FormItem
                          className="flex items-center space-x-3 space-y-0"
                          key={idx}
                        >
                          <FormControl>
                            <RadioGroupItem value={v.value} />
                          </FormControl>
                          <FormLabel className="font-normal">
                            {v.name}
                          </FormLabel>
                        </FormItem>
                      ))}
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="organization_identifier_id"
              render={({ field }) => (
                <FormItem className="w-[20vw]">
                  <FormLabel htmlFor="email" className="text-lg font-semibold">
                    Employee ID (Optional)
                  </FormLabel>
                  <p className="text-xs text-neutral-500">
                    Unique identifier assigned to each employee which is used
                    for tracking and management purposes.
                  </p>
                  <FormControl>
                    <Input
                      placeholder="Enter the employee ID"
                      className="w-full"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex gap-4">
              <Link href={PATH.DASHBOARD_USER_MANAGEMENT}>
                <Button variant="outline" type="button">
                  Back
                </Button>
              </Link>
              <Button
                className="bg-gradient"
                type="submit"
                isLoading={createUserManagementInvite.isPending}
              >
                Send Invites
              </Button>
            </div>
          </form>
        </Form>
      </section>
    </div>
  );
}

export default InvitePage;
