import { queryOptions, useQuery } from "@tanstack/react-query";
import { useMemo } from "react";

import { api } from "@/lib/api-client";
import { ApiResponse, BaseParams, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { IndustryData } from "../types";

type IndustryListParams = BaseParams & { ap_active?: boolean };

export const getIndustryList = ({
  params,
}: {
  params?: IndustryListParams;
}): ApiResponse<IndustryData[]> => {
  return api.get(API_ROUTES.INDUSTRIES, { params });
};

export const getIndustryListQueryOptions = (params?: IndustryListParams) => {
  return queryOptions({
    queryKey: [QUERY_KEYS.INDUSTRIES, params],
    queryFn: () => getIndustryList({ params }),
  });
};

type UseIndustryListOptions = {
  params?: IndustryListParams;
  queryConfig?: QueryConfig<typeof getIndustryList>;
};

export const useIndustryList = ({
  queryConfig,
  params,
}: UseIndustryListOptions) => {
  const industryListQuery = useQuery({
    ...getIndustryListQueryOptions(params),
    ...queryConfig,
  });

  const industryOptions = useMemo(() => {
    return (
      industryListQuery.data?.data.map((v) => ({
        label: v.name,
        value: v.name,
        id: v.id,
      })) ?? []
    );
  }, [industryListQuery.data?.data]);

  return {
    ...industryListQuery,
    industryList: industryListQuery.data?.data || [],
    industryOptions,
  };
};
