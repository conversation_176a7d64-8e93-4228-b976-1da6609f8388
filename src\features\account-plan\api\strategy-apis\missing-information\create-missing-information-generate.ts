import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";

export const generateMissingInformation = ({
  accountId,
  data,
}: {
  accountId: number;
  data?: {
    tz?: string;
  };
}): ApiResponse => {
  return api.post(
    API_ROUTES.ACCOUNT_PLANS_MISSING_INFORMATION_GENERATE(accountId),
    data
  );
};

type UseGenerateMissingInformationOptions = {
  mutationConfig?: MutationConfig<typeof generateMissingInformation>;
};

export const useGenerateMissingInformation = ({
  mutationConfig,
}: UseGenerateMissingInformationOptions) => {
  const queryClient = useQueryClient();
  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      await queryClient.invalidateQueries({
        queryKey: [
          QUERY_KEYS.ACCOUNT_PLANS,
          args[1].accountId,
          QUERY_KEYS.ACCOUNT_PLANS_MISSING_INFORMATION,
        ],
      });

      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: generateMissingInformation,
  });
};
