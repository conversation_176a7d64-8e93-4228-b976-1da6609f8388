import _ from "lodash";

import {
  PdfTable,
  PdfTableCell,
  PdfTableHeader,
  PdfTableRow,
  PdfTableTitle,
} from "..";
import { AccountPlanAnalysisPDFProps } from "../../download-analysis";
import { formatDecimalValue } from "@/lib/utils/table-utils";
import { getAccountPlanTableName } from "@/features/account-plan/constants";
import { AccountPlanTableType } from "@/features/account-plan/types";

export const RevenueForecastPDFTable = ({
  data,
}: {
  data?: AccountPlanAnalysisPDFProps["revenueForecastList"];
}) => {
  return (
    <PdfTable>
      <PdfTableTitle>
        {" "}
        {getAccountPlanTableName(AccountPlanTableType.REVENUE_FORECAST)}
      </PdfTableTitle>

      <PdfTableRow>
        <PdfTableHeader style={{ flex: 0.5 }}>Future</PdfTableHeader>
        <PdfTableHeader>Low</PdfTableHeader>
        <PdfTableHeader>Realistic</PdfTableHeader>
        <PdfTableHeader>High</PdfTableHeader>
      </PdfTableRow>

      {data?.map((row, idx) => (
        <PdfTableRow key={idx}>
          <PdfTableCell style={{ flex: 0.5 }}>
            {row.timespan} months
          </PdfTableCell>
          <PdfTableCell>
            {formatDecimalValue({ value: row.low_scenario })}
          </PdfTableCell>
          <PdfTableCell>
            {formatDecimalValue({ value: row.realistic_scenario })}
          </PdfTableCell>
          <PdfTableCell>
            {formatDecimalValue({ value: row.high_scenario })}
          </PdfTableCell>
        </PdfTableRow>
      ))}
    </PdfTable>
  );
};
