"use client";

import { IconP<PERSON>cil, IconUserCircle } from "@tabler/icons-react";
import Link from "next/link";
import { usePara<PERSON>, useRouter, useSearchParams } from "next/navigation";
import React, { useEffect, useRef, useState } from "react";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { PATH } from "@/constants/path";
import { useUser } from "@/features/auth/api/user/get-user";
import { formatDate, getFullName } from "@/lib/utils";
import { useUpdateUser } from "@/features/auth/api/user/update-user";
import { User, UserStatus } from "@/features/auth/types/user";
import { useDeleteUser } from "@/features/auth/api/user/delete-user";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useAuth } from "@/features/auth/api/get-auth";
import { useIsHydrated } from "@/lib/hooks/use-hydrated";
import { useLogout } from "@/features/auth/api/logout";
import { getRoleAlias } from "@/features/user-management/constants";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { isRequestError } from "@/lib/api-client";
import { z } from "zod";
import { Input } from "@/components/ui/input";

const formSchema = z.object({
  name: z.string().min(1, { message: "Please enter the first name" }),
  last_name: z.string().optional(),
});

type FormSchema = z.infer<typeof formSchema>;

function EditNameDialog({
  user,
  organizationId,
}: {
  user: User;
  organizationId: number;
}) {
  const { isOwner } = useAuth({});
  const form = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    reValidateMode: "onSubmit",
  });
  const triggerRef = useRef<HTMLButtonElement | null>(null);

  const updateUser = useUpdateUser({
    mutationConfig: {
      invalidate: true,
    },
  });

  const onEditName = async (values: FormSchema) => {
    try {
      await updateUser.mutateAsync({
        userId: user.id,
        data: {
          ...values,
          ...(isOwner && { organization_id: organizationId }),
        },
      });

      triggerRef.current?.click();
      toast(`User name has been updated`);
    } catch (e) {
      if (isRequestError(e)) {
        const errorMessage = e.response?.data.errors[0].message ?? "";
        toast.error(errorMessage);
      } else {
        toast.error("An error occured while updating user name");
      }
    }
  };

  useEffect(() => {
    form.setValue("name", user.first_name ?? "");
    form.setValue("last_name", user.last_name ?? "");
  }, [form, user]);

  return (
    <Dialog>
      <DialogTrigger ref={triggerRef}>
        <IconPencil className="text-primary-500" />
      </DialogTrigger>
      <DialogContent>
        <DialogTitle>Edit User Name</DialogTitle>
        <Form {...form}>
          <form
            className="mt-4 min-w-[20vw]"
            onSubmit={form.handleSubmit(onEditName)}
          >
            <div className="mb-6 grid gap-6">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel htmlFor="name" className="font-semibold">
                      First Name
                    </FormLabel>
                    <FormControl>
                      <Input
                        id="name"
                        placeholder="Enter your first name"
                        className="w-full"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="last_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel htmlFor="last_name" className="font-semibold">
                      Last Name (Optional)
                    </FormLabel>
                    <FormControl>
                      <Input
                        id="last_name"
                        placeholder="Enter your last name"
                        className="w-full"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="flex">
              <Button
                type="submit"
                isLoading={updateUser.isPending}
                className="ml-auto"
              >
                Update User Name
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

function UserManagement() {
  const { id } = useParams<{ id: string }>();
  const userId = parseInt(id);
  const searchParams = useSearchParams();
  const organization_id = Number(searchParams.get("organization_id")) ?? null;

  const { isHydrated } = useIsHydrated();
  const logout = useLogout();
  const { isOwnAccount: checkIsOwnAccount, isOwner } = useAuth({});
  const { user, failureReason } = useUser({
    userId,
    params: {
      ...(isOwner && { organization_id }),
    },
  });
  const updateUser = useUpdateUser({
    mutationConfig: {
      invalidate: true,
    },
  });
  const deleteUser = useDeleteUser({});
  const router = useRouter();

  const [isShowModalDelete, setIsShowModalDelete] = useState(false);
  const [isShowModalArchive, setIsShowModalArchive] = useState(false);

  const isOwnAccount = checkIsOwnAccount(userId);
  const isActiveUser = user?.status === UserStatus.ACTIVE;
  const userFullname = getFullName(user?.first_name, user?.last_name);

  const onDeleteUser = async () => {
    try {
      await deleteUser.mutateAsync({
        userId,
        params: {
          ...(isOwner && { organization_id }),
        },
      });

      toast(`${userFullname} has been removed`);

      if (isOwnAccount) {
        logout();
      } else {
        router.push(PATH.DASHBOARD_USER_MANAGEMENT);
      }

      setIsShowModalDelete(false);
    } catch (_) {
      toast("An error occured while removing the user");
    }
  };

  const onArchiveUser = async () => {
    try {
      await updateUser.mutateAsync({
        userId,
        data: {
          status: isActiveUser ? UserStatus.ARCHIVED : UserStatus.ACTIVE,
          organization_id,
        },
      });

      toast(
        `${userFullname} has been ${isActiveUser ? "archived" : "activated"}`
      );

      if (isOwnAccount) {
        logout();
      }

      setIsShowModalArchive(false);
    } catch (_) {
      toast("An error occured while archiving the user");
    }
  };

  if (!isHydrated) return null;

  return (
    <div className="w-fit">
      <div className="mb-8 flex justify-between">
        <div className="grid gap-2">
          <h1 className="text-3xl font-bold text-primary-500">Edit User</h1>
          <p className="text-2xl font-semibold text-secondary-500">
            Select the permissions
          </p>
        </div>
      </div>

      <section className="grid gap-res-y-lg">
        <div className="flex items-center">
          <Avatar className="ml-res-x-xs mr-res-x-base h-[8vh] w-[8vh] rounded-full">
            <AvatarImage src={user?.photo_url ?? ""} />
            <AvatarFallback className="bg-transparent">
              <IconUserCircle className="h-full w-full" />
            </AvatarFallback>
          </Avatar>

          <div>
            <div className="mb-res-y-xs flex items-center gap-res-x-xs">
              <h2 className="text-2xl font-bold text-primary-500">
                {userFullname}
              </h2>
              {!!user && (
                <EditNameDialog user={user} organizationId={organization_id} />
              )}
            </div>
            <p className="font-semibold text-primary-500">
              {getRoleAlias(user?.role)}
            </p>
            <p className="font-semibold text-primary-500">
              Added:{" "}
              <span className="font-normal">
                {formatDate(user?.created_at)}
              </span>
            </p>
          </div>
        </div>

        <div className="flex w-full gap-res-x-sm">
          <Dialog open={isShowModalDelete} onOpenChange={setIsShowModalDelete}>
            <DialogTrigger>
              <Button variant="destructive">Delete User</Button>
            </DialogTrigger>

            <DialogContent>
              <DialogClose />
              <DialogTitle>Delete User</DialogTitle>
              <DialogDescription>
                Are you sure want to delete{" "}
                {isOwnAccount ? (
                  <>
                    your own account? <br />
                    You will be logged out after this action.
                  </>
                ) : (
                  "this account?"
                )}
              </DialogDescription>

              <DialogFooter>
                <Button
                  onClick={onDeleteUser}
                  isLoading={deleteUser.isPending}
                  variant="destructive"
                >
                  Delete User
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Dialog
            open={isShowModalArchive}
            onOpenChange={setIsShowModalArchive}
          >
            <DialogTrigger>
              <Button variant={isActiveUser ? "default" : "outline"}>
                {isActiveUser ? "Archive" : "Activate"} User
              </Button>
            </DialogTrigger>

            <DialogContent>
              <DialogClose />
              <DialogTitle>
                {isActiveUser ? "Archive" : "Activate"} User
              </DialogTitle>
              <DialogDescription>
                Are you sure want to {isActiveUser ? "archive" : "activate"}{" "}
                {isOwnAccount ? (
                  <>
                    your own account?
                    <br />
                    You will be logged out after this action.
                  </>
                ) : (
                  "this account?"
                )}
              </DialogDescription>

              <DialogFooter>
                <Button
                  onClick={onArchiveUser}
                  isLoading={updateUser.isPending}
                >
                  {isActiveUser ? "Archive" : "Activate"} User
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Link
            href={`${PATH.DASHBOARD_USER_MANAGEMENT}?organization_id=${organization_id}`}
          >
            <Button variant="neutral">Back</Button>
          </Link>
        </div>
      </section>

      <Dialog open={failureReason?.status === 404}>
        <DialogContent closeClassName="hidden">
          <DialogClose />
          <DialogTitle>User Not Found</DialogTitle>
          <DialogDescription>
            User with <strong>ID: {userId}</strong> doesn't exist
          </DialogDescription>

          <DialogFooter>
            <Link
              href={`${PATH.DASHBOARD_USER_MANAGEMENT}?organization_id=${organization_id}`}
            >
              <Button>Back to User Management</Button>
            </Link>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default UserManagement;
