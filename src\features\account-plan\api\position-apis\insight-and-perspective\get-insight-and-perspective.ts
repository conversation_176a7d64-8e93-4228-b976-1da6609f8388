import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { APInsightAndPerspective } from "@/features/account-plan/types/position-types";

export const getInsightAndPerspectiveDetail = ({
  id,
  accountId,
}: {
  accountId: number;
  id: number;
}): ApiResponse<APInsightAndPerspective> => {
  return api.get(
    API_ROUTES.ACCOUNT_PLANS_INSIGHT_AND_PERSPECTIVE_DETAIL(accountId, id)
  );
};

export const getInsightAndPerspectiveDetailQueryOptions = (
  accountId: number,
  id: number
) => {
  return queryOptions({
    queryKey: [
      QUERY_KEYS.ACCOUNT_PLANS,
      accountId,
      QUERY_KEYS.ACCOUNT_PLANS_INSIGHT_AND_PERSPECTIVE,
      id,
    ],
    queryFn: () => getInsightAndPerspectiveDetail({ accountId, id }),
    enabled: !!accountId && !!id,
  });
};

type UseInsightAndPerspectiveDetailOptions = {
  id: number;
  accountId: number;
  queryConfig?: QueryConfig<typeof getInsightAndPerspectiveDetail>;
  options?: Partial<
    ReturnType<typeof getInsightAndPerspectiveDetailQueryOptions>
  >;
};

export const useInsightAndPerspectiveDetail = ({
  id,
  accountId,
  queryConfig,
  options,
}: UseInsightAndPerspectiveDetailOptions) => {
  const insightAndPerspectiveDetailQuery = useQuery({
    ...getInsightAndPerspectiveDetailQueryOptions(accountId, id),
    ...queryConfig,
    ...options,
  });

  return {
    ...insightAndPerspectiveDetailQuery,
    insightAndPerspectiveDetail: insightAndPerspectiveDetailQuery.data?.data,
  };
};
