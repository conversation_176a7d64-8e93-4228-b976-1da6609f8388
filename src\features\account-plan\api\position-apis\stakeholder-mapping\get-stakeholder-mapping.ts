import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { APStakeholderMapping } from "@/features/account-plan/types/position-types";

export const getStakeholderMappingDetail = ({
  id,
  accountId,
}: {
  accountId: number;
  id: number;
}): ApiResponse<APStakeholderMapping> => {
  return api.get(
    API_ROUTES.ACCOUNT_PLANS_STAKEHOLDER_MAPPING_DETAIL(accountId, id)
  );
};

export const getStakeholderMappingDetailQueryOptions = (
  accountId: number,
  id: number
) => {
  return queryOptions({
    queryKey: [
      QUERY_KEYS.ACCOUNT_PLANS,
      accountId,
      QUERY_KEYS.ACCOUNT_PLANS_STAKEHOLDER_MAPPING,
      id,
    ],
    queryFn: () => getStakeholderMappingDetail({ accountId, id }),
    enabled: !!accountId && !!id,
  });
};

type UseStakeholderMappingDetailOptions = {
  id: number;
  accountId: number;
  queryConfig?: QueryConfig<typeof getStakeholderMappingDetail>;
  options?: Partial<ReturnType<typeof getStakeholderMappingDetailQueryOptions>>;
};

export const useStakeholderMappingDetail = ({
  id,
  accountId,
  queryConfig,
  options,
}: UseStakeholderMappingDetailOptions) => {
  const stakeholderMappingDetailQuery = useQuery({
    ...getStakeholderMappingDetailQueryOptions(accountId, id),
    ...queryConfig,
    ...options,
  });

  return {
    ...stakeholderMappingDetailQuery,
    stakeholderMappingDetail: stakeholderMappingDetailQuery.data?.data,
  };
};
