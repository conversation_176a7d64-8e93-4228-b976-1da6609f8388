import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, BaseParams, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { APTargetedPerceptionDevelopment } from "@/features/account-plan/types/strategy-types";

type TargetedPerceptionDevelopmenListParams = BaseParams;

export const getTargetedPerceptionDevelopmentList = ({
  accountId,
  params,
}: {
  accountId: number;
  params?: TargetedPerceptionDevelopmenListParams;
}): ApiResponse<APTargetedPerceptionDevelopment[]> => {
  return api.get(
    API_ROUTES.ACCOUNT_PLANS_TARGETED_PERCEPTION_DEVELOPMENT(accountId),
    {
      params,
    }
  );
};

export const getTargetedPerceptionDevelopmentListQueryOptions = (
  accountId: number,
  params?: TargetedPerceptionDevelopmenListParams
) => {
  return queryOptions({
    queryKey: [
      QUERY_KEYS.ACCOUNT_PLANS,
      accountId,
      QUERY_KEYS.ACCOUNT_PLANS_TARGETED_PERCEPTION_DEVELOPMENT,
    ],
    queryFn: () => getTargetedPerceptionDevelopmentList({ accountId, params }),
    enabled: !!accountId,
  });
};

type UseTargetedPerceptionDevelopmentListOptions = {
  params?: TargetedPerceptionDevelopmenListParams;
  accountId: number;
  queryConfig?: QueryConfig<typeof getTargetedPerceptionDevelopmentList>;
  options?: Partial<
    ReturnType<typeof getTargetedPerceptionDevelopmentListQueryOptions>
  >;
};

export const useTargetedPerceptionDevelopmentList = ({
  params,
  accountId,
  queryConfig,
  options,
}: UseTargetedPerceptionDevelopmentListOptions) => {
  const targetedPerceptionDevelopmentListQuery = useQuery({
    ...getTargetedPerceptionDevelopmentListQueryOptions(accountId, params),
    ...queryConfig,
    ...options,
  });

  return {
    ...targetedPerceptionDevelopmentListQuery,
    targetedPerceptionDevelopmentList:
      targetedPerceptionDevelopmentListQuery.data?.data,
  };
};
