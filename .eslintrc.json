{"extends": ["next/core-web-vitals", "plugin:@typescript-eslint/recommended", "plugin:react/recommended", "prettier"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "settings": {"react": {"version": "detect"}}, "rules": {"react-hooks/exhaustive-deps": ["warn", {"additionalHooks": "(useDeepCompareEffect|useDeepCompareCallback|useDeepCompareMemo|useDeepCompareImperativeHandle|useDeepCompareLayoutEffect)"}], "no-unused-vars": "off", "@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_", "caughtErrorsIgnorePattern": "^_"}], "@typescript-eslint/no-empty-object-type": "off", "@typescript-eslint/ban-ts-comment": "off", "react/prop-types": "off", "react/react-in-jsx-scope": "off", "react/no-unescaped-entities": "off", "prettier/prettier": ["error", {"endOfLine": "auto"}]}, "plugins": ["@typescript-eslint", "react-hooks", "prettier"]}