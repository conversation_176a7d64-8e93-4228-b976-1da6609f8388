"use client";

import Image from "next/image";
import Link from "next/link";
import React from "react";

import PsAiColor<PERSON>ogo from "@/assets/ps-ai-color-logo.png";
import { PATH } from "@/constants/path";
import { Button } from "@/components/ui/button";

export function UserAgreementList({ children }: { children: React.ReactNode }) {
  return (
    <ul className="ml-4 list-outside list-disc space-y-1 pl-6">{children}</ul>
  );
}

export function UserAgreementItem({
  title,
  children,
}: {
  title: string;
  children: React.ReactNode;
}) {
  return (
    <div className="space-y-2">
      <h2 className="text-2xl font-semibold">{title}</h2>
      {children}
    </div>
  );
}

export function UserAgreementsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="mx-auto my-8 w-full max-w-screen-lg px-4 text-primary-500 lg:px-8">
      <div className="float-right mb-6">
        <Link href={PATH.LANDING}>
          <Button className="bg-gradient p-6 px-10">Home</Button>
        </Link>
      </div>

      <div className="my-12 hyphens-auto text-justify md:hyphens-none">
        {children}
      </div>
    </div>
  );
}
