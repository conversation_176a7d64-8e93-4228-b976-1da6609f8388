import { queryOptions, useQueries, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, BaseParams, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { APActionPlan } from "@/features/account-plan/types/strategy-types";

type ActionPlanListParams = BaseParams;

export const getActionPlanList = ({
  accountId,
  params,
}: {
  accountId: number;
  params?: ActionPlanListParams;
}): ApiResponse<APActionPlan[]> => {
  return api.get(API_ROUTES.ACCOUNT_PLANS_ACTION_PLAN(accountId), {
    params,
  });
};

export const getActionPlanListQueryOptions = (
  accountId: number,
  params?: ActionPlanListParams
) => {
  return queryOptions({
    queryKey: [
      QUERY_KEYS.ACCOUNT_PLANS,
      accountId,
      QUERY_KEYS.ACCOUNT_PLANS_ACTION_PLAN,
    ],
    queryFn: () => getActionPlanList({ accountId, params }),
    enabled: !!accountId,
  });
};

type UseActionPlanListOptions = {
  params?: ActionPlanListParams;
  accountId: number;
  queryConfig?: QueryConfig<typeof getActionPlanList>;
  options?: Partial<ReturnType<typeof getActionPlanListQueryOptions>>;
};

export const useActionPlanList = ({
  params,
  accountId,
  queryConfig,
  options,
}: UseActionPlanListOptions) => {
  const actionPlanListQuery = useQuery({
    ...getActionPlanListQueryOptions(accountId, params),
    ...queryConfig,
    ...options,
  });

  return {
    ...actionPlanListQuery,
    actionPlanList: actionPlanListQuery.data?.data,
  };
};

type UseActionPlanListsOptions = {
  queries: {
    accountId: number;
    params?: ActionPlanListParams;
  }[];
  queryConfig?: QueryConfig<typeof getActionPlanList>;
  options?: Partial<ReturnType<typeof getActionPlanListQueryOptions>>;
};

export const useActionPlanLists = ({
  queries,
  queryConfig,
}: UseActionPlanListsOptions) => {
  const results = useQueries({
    queries: queries.map(({ accountId, params }) => ({
      ...getActionPlanListQueryOptions(accountId, params),
      ...queryConfig,
    })),
  });

  return results.map((result, idx) => ({
    ...result,
    query: queries[idx],
    actionPlanList: result.data?.data,
  }));
};
