import React from "react";
import { Badge } from "../badge";
import { capitalize, cn } from "@/lib/utils";

const badgeBg = {
  Active: "bg-green-500",
  active: "bg-green-500",
  Inactive: "bg-slate-500",
  inactive: "bg-slate-500",
} as const;

export type StatusBadgeProps = { status: keyof typeof badgeBg };

export const StatusBadge = ({ status }: StatusBadgeProps) => {
  return (
    <Badge
      className={cn(
        "min-w-[80px] text-white",
        badgeBg[status] ?? "bg-slate-500"
      )}
      variant="outline"
    >
      {capitalize(status)}
    </Badge>
  );
};
