import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { APCircumstantialAnalysis } from "@/features/account-plan/types/position-types";

export const getCircumstantialAnalysisDetail = ({
  id,
  accountId,
}: {
  accountId: number;
  id: number;
}): ApiResponse<APCircumstantialAnalysis> => {
  return api.get(
    API_ROUTES.ACCOUNT_PLANS_CIRCUMSTANTIAL_ANALYSIS_DETAIL(accountId, id)
  );
};

export const getCircumstantialAnalysisDetailQueryOptions = (
  accountId: number,
  id: number
) => {
  return queryOptions({
    queryKey: [
      QUERY_KEYS.ACCOUNT_PLANS,
      accountId,
      QUERY_KEYS.ACCOUNT_PLANS_CIRCUMSTANTIAL_ANALYSIS,
      id,
    ],
    queryFn: () => getCircumstantialAnalysisDetail({ accountId, id }),
    enabled: !!accountId && !!id,
  });
};

type UseCircumstantialAnalysisDetailOptions = {
  id: number;
  accountId: number;
  queryConfig?: QueryConfig<typeof getCircumstantialAnalysisDetail>;
  options?: Partial<
    ReturnType<typeof getCircumstantialAnalysisDetailQueryOptions>
  >;
};

export const useCircumstantialAnalysisDetail = ({
  id,
  accountId,
  queryConfig,
  options,
}: UseCircumstantialAnalysisDetailOptions) => {
  const circumstantialAnalysisDetailQuery = useQuery({
    ...getCircumstantialAnalysisDetailQueryOptions(accountId, id),
    ...queryConfig,
    ...options,
  });

  return {
    ...circumstantialAnalysisDetailQuery,
    circumstantialAnalysisDetail: circumstantialAnalysisDetailQuery.data?.data,
  };
};
