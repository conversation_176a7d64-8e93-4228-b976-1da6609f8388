import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";

export const deleteCurrentOpportunity = ({
  accountId,
  id,
}: {
  id: number;
  accountId: number;
}): ApiResponse => {
  return api.delete(
    API_ROUTES.ACCOUNT_PLANS_CURRENT_OPPORTUNITY_DETAIL(accountId, id)
  );
};

type UseDeleteCurrentOpportunityOptions = {
  mutationConfig?: MutationConfig<typeof deleteCurrentOpportunity>;
};

export const useDeleteCurrentOpportunity = ({
  mutationConfig,
}: UseDeleteCurrentOpportunityOptions) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      await queryClient.invalidateQueries({
        queryKey: [
          QUERY_KEYS.ACCOUNT_PLANS,
          args[1].accountId,
          QUERY_KEYS.ACCOUNT_PLANS_CURRENT_OPPORTUNITY,
        ],
      });

      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: deleteCurrentOpportunity,
  });
};
