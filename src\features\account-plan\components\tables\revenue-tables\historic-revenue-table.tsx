import { type ColumnDef } from "@tanstack/react-table";
import { useParams } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";
import _ from "lodash";
import { toast } from "sonner";

import {
  AccountPlanData,
  AccountPlanTableType,
} from "@/features/account-plan/types";
import DataTable, { DataTableMeta } from "@/components/ui/data-table";
import {
  EditableNumberCell,
  SelectCell,
  TiptapCell,
} from "@/components/ui/data-table/data-table-components";
import { APHistoricRevenue } from "@/features/account-plan/types/revenue-types";
import { useHistoricRevenueList } from "@/features/account-plan/api/revenue-apis/historic-revenue/get-historic-revenue-list";
import { useUpdateHistoricRevenue } from "@/features/account-plan/api/revenue-apis/historic-revenue/update-historic-revenue";

import { AccountTable, AccountTableTitle } from "../base-table";
import { Button } from "@/components/ui/button";
import { useCreateHistoricRevenue } from "@/features/account-plan/api/revenue-apis/historic-revenue/create-historic-revenue";
import { useDeleteHistoricRevenue } from "@/features/account-plan/api/revenue-apis/historic-revenue/delete-historic-revenue";
import { getAccountPlanTableName } from "@/features/account-plan/constants";

export const HistoricRevenueTable = ({
  accountPlan,
}: {
  accountPlan?: AccountPlanData;
}) => {
  const [tableData, setTableData] = useState<APHistoricRevenue[]>([]);
  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({});

  const { id } = useParams<{ id: string }>();
  const accountId = parseInt(id);

  const { historicRevenueList } = useHistoricRevenueList({
    accountId,
    params: {
      disable_pagination: true,
    },
  });
  const createHistoricRevenue = useCreateHistoricRevenue({});
  const updateHistoricRevenue = useUpdateHistoricRevenue({});
  const deleteHistoricRevenue = useDeleteHistoricRevenue({});

  useEffect(() => {
    if (!historicRevenueList) return;

    const newTableData = historicRevenueList?.map((v, idx) => ({
      idx,
      ...v,
    }));

    setTableData(newTableData);
  }, [historicRevenueList]);

  const selectedRows = Object.keys(rowSelection)
    .filter((rowId) => rowSelection[rowId])
    .map((idx) => tableData[parseInt(idx)]);

  const onAddRow = async () => {
    try {
      const res = await createHistoricRevenue.mutateAsync({
        accountId,
      });

      setTableData((prev) => [...prev, res.data]);
    } catch (_) {
      toast("An unexpected error occured when adding data");
    }
  };

  const onDeleteRows = async () => {
    try {
      const promises = [];

      setTableData(
        tableData.filter((row) => !selectedRows.find((v) => v.id === row.id))
      );

      setRowSelection({});

      promises.push(
        selectedRows.map(async (row) => {
          if (!!row?.id) {
            return deleteHistoricRevenue.mutateAsync({
              id: row.id,
              accountId,
            });
          }
        })
      );
      await Promise.all(promises);
    } catch (_) {
      toast("An unexpected error occured when deleting rows");
    }
  };

  const onChangeData = useCallback(
    async (data: Partial<APHistoricRevenue>, id: number) => {
      try {
        setTableData((prev) =>
          prev.map((v) => (v.id === id ? { ...v, ...data } : v))
        );

        await updateHistoricRevenue.mutateAsync({
          accountId,
          id,
          data,
        });
      } catch (_) {
        toast("An unexpected error occured when modifying data");
      }
    },
    [accountId, updateHistoricRevenue]
  );

  const columns: ColumnDef<APHistoricRevenue>[] = useMemo(
    () => [
      {
        accessorKey: "time_month",
        header: "Past",
        size: 200,
        cell: ({ row, table }) => {
          const isPreview =
            (table.options.meta as DataTableMeta)?.isPreview ?? false;
          const rowData = row.original;

          return (
            <SelectCell
              isPreview={isPreview}
              value={rowData.time_month}
              onChange={(time_month) => {
                onChangeData({ time_month }, rowData.id);
              }}
              options={[3, 6, 9, 12, 18, 24, 36].map((v) => ({
                label: `${v} months`,
                value: v.toString(),
              }))}
              className={isPreview ? "text-4xl" : "text-lg"}
            />
          );
        },
        meta: {
          tooltip:
            "The period for which the revenue forecast is being projected, helping track short-term or long-term expectations.",
        },
      },
      {
        accessorKey: "product_service_name",
        header: "Service and Product Description",
        size: 600,
        cell: ({ row, table }) => {
          const isPreview =
            (table.options.meta as DataTableMeta)?.isPreview ?? false;
          const rowData = row.original;

          return (
            <TiptapCell
              value={rowData.product_service_name}
              onChange={(product_service_name) =>
                onChangeData({ product_service_name }, rowData.id)
              }
              placeholder="Input service and product description"
              className={isPreview ? "text-4xl" : "text-lg"}
            />
          );
        },
        meta: {
          tooltip:
            "The specific products or services that contributed to the account's revenue during the selected time period.",
        },
      },
      {
        accessorKey: "value",
        header: "Currency Value",
        size: 200,
        cell: ({ row, table }) => {
          const isPreview =
            (table.options.meta as DataTableMeta)?.isPreview ?? false;
          const rowData = row.original;

          return (
            <EditableNumberCell
              className={isPreview ? "text-4xl" : "text-lg"}
              value={rowData.value}
              onChange={(value) => {
                onChangeData({ value }, rowData.id);
              }}
            />
          );
        },
        meta: {
          tooltip:
            "The total monetary value of revenue generated by the account during the specified time period.",
        },
      },
    ],
    [onChangeData]
  );

  return (
    <AccountTable
      type={AccountPlanTableType.HISTORIC_REVENUE}
      footer={
        <>
          <Button
            onClick={onAddRow}
            isLoading={createHistoricRevenue.isPending}
          >
            Add row
          </Button>
          <Button
            variant="destructive"
            disabled={selectedRows.length === 0}
            onClick={onDeleteRows}
          >
            Delete Row
          </Button>
        </>
      }
    >
      <DataTable
        columns={columns}
        data={tableData}
        rowSelection={rowSelection}
        setRowSelection={setRowSelection}
        size="m"
        currency={accountPlan?.account_plan_group?.currency}
        tableType={AccountPlanTableType.HISTORIC_REVENUE}
      />
    </AccountTable>
  );
};
