import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { APTopAction } from "@/features/account-plan/types/strategy-types";

export const getTopActionDetail = ({
  id,
  accountId,
}: {
  accountId: number;
  id: number;
}): ApiResponse<APTopAction> => {
  return api.get(API_ROUTES.ACCOUNT_PLANS_TOP_ACTION_DETAIL(accountId, id));
};

export const getTopActionDetailQueryOptions = (
  accountId: number,
  id: number
) => {
  return queryOptions({
    queryKey: [
      QUERY_KEYS.ACCOUNT_PLANS,
      accountId,
      QUERY_KEYS.ACCOUNT_PLANS_TOP_ACTION,
      id,
    ],
    queryFn: () => getTopActionDetail({ accountId, id }),
    enabled: !!accountId && !!id,
  });
};

type UseTopActionDetailOptions = {
  id: number;
  accountId: number;
  queryConfig?: QueryConfig<typeof getTopActionDetail>;
  options?: Partial<ReturnType<typeof getTopActionDetailQueryOptions>>;
};

export const useTopActionDetail = ({
  id,
  accountId,
  queryConfig,
  options,
}: UseTopActionDetailOptions) => {
  const topActionDetailQuery = useQuery({
    ...getTopActionDetailQueryOptions(accountId, id),
    ...queryConfig,
    ...options,
  });

  return {
    ...topActionDetailQuery,
    TopActionDetail: topActionDetailQuery.data?.data,
  };
};
