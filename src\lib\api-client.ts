import Axios, { InternalAxiosRequestConfig } from "axios";

import { useAuthStore } from "@/features/auth/stores/auth-store";
import { ErrorResponse } from "@/types/api";
import { PATH } from "@/constants/path";
import { STORAGE_KEYS } from "@/constants/storage-keys";
import { API_ROUTES } from "@/constants/api-routes";

function authRequestInterceptor(config: InternalAxiosRequestConfig) {
  const { refreshToken } = useAuthStore.getState();

  if (config.headers) {
    config.headers.Accept = "application/json";
    config.headers.Authorization = `Bearer ${refreshToken}`;
  }

  // Only set withCredentials for production to avoid CORS issues in local development
  config.withCredentials = process.env.NODE_ENV === "production";
  return config;
}

export const isRequestError = (e: unknown) =>
  Axios.isAxiosError<ErrorResponse>(e);

export const api = Axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
});

api.interceptors.request.use(authRequestInterceptor);
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    console.log(error);
    if (
      error.response?.status === 401 &&
      !error.request?.responseURL.includes(API_ROUTES.ORGANIZATIONS)
    ) {
      localStorage.removeItem(STORAGE_KEYS.AUTH_STORE);

      if (!error.request?.responseURL.includes(API_ROUTES.LOGIN)) {
        window.location.href = PATH.LANDING;
      }
    }
    return Promise.reject(error);
  }
);
