import { Command as CommandPrimitive } from "cmdk";
import React, {
  useState,
  useRef,
  useCallback,
  type KeyboardEvent,
  useEffect,
  useMemo,
} from "react";
import { IconCheck } from "@tabler/icons-react";

import { cn } from "@/lib/utils";

import { Skeleton } from "./skeleton";
import {
  CommandGroup,
  CommandItem,
  CommandList,
  CommandInput,
} from "./command";
import mergeRefs from "merge-refs";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type Option = Record<"value" | "label", string> & Record<string, any>;

type AutoCompleteProps = {
  options: Option[];
  emptyMessage?: string;
  value?: Option;
  onValueChange?: (value: Option) => void;
  isLoading?: boolean;
  disabled?: boolean;
  placeholder?: string;
  onFocus?: () => void;
  onBlur?: () => void;
};

export const AutoComplete = React.forwardRef<
  HTMLInputElement,
  AutoCompleteProps
>(
  (
    {
      options,
      placeholder,
      emptyMessage = "No Results",
      value,
      onValueChange,
      disabled,
      isLoading = false,
      onFocus,
      onBlur,
    },
    ref
  ) => {
    const inputRef = useRef<HTMLInputElement>(null);

    const [inputValue, setInputValue] = useState("");
    const [isOpen, setOpen] = useState(false);

    const handleKeyDown = useCallback(
      (event: KeyboardEvent<HTMLDivElement>) => {
        const input = inputRef.current;

        if (!input) {
          return;
        }

        if (!isOpen) {
          setOpen(true);
        }

        if (event.key === "Enter" && input.value !== "") {
          const optionToSelect = options.find(
            (option) => option.label === input.value
          );
          if (optionToSelect) {
            onValueChange?.(optionToSelect);
          }
        }

        if (event.key === "Escape") {
          input.blur();
        }
      },
      [isOpen, options, onValueChange]
    );

    const handleSelectOption = useCallback(
      (selectedOption: Option) => {
        onValueChange?.(selectedOption);

        setInputValue(selectedOption.label);

        setTimeout(() => {
          inputRef?.current?.blur();
        }, 0);
      },
      [onValueChange]
    );

    const filteredOptions = useMemo(() => {
      if (!inputValue.trim()) return options;
      return options.filter((option) =>
        option.label.toLowerCase().includes(inputValue.toLowerCase())
      );
    }, [inputValue, options]);

    useEffect(() => {
      if (value?.value) {
        setInputValue(value?.value);
      }
    }, [value?.value]);

    return (
      <CommandPrimitive
        onKeyDown={handleKeyDown}
        className="h-[4.5vh]"
        shouldFilter={false}
      >
        <div>
          <CommandInput
            ref={mergeRefs(inputRef, ref)}
            value={inputValue}
            onValueChange={setInputValue}
            onBlur={() => {
              onBlur?.();
              setOpen(false);
            }}
            onFocus={() => {
              onFocus?.();
              setOpen(true);
            }}
            placeholder={placeholder}
            disabled={disabled}
            className="bg-white"
          />
        </div>
        <div className="relative mt-1">
          <div
            className={cn(
              "absolute top-0 z-10 w-full rounded-xl bg-white outline-none animate-in fade-in-0 zoom-in-95",
              isOpen ? "block" : "hidden"
            )}
          >
            <CommandList className="rounded-lg ring-1 ring-slate-200">
              {isLoading ? (
                <CommandPrimitive.Loading>
                  <div className="p-1">
                    <Skeleton className="h-8 w-full" />
                  </div>
                </CommandPrimitive.Loading>
              ) : null}
              {filteredOptions.length > 0 && !isLoading ? (
                <CommandGroup>
                  {filteredOptions.map((option) => {
                    const isSelected = value?.value === option.value;

                    return (
                      <CommandItem
                        key={option.value}
                        value={option.label}
                        onMouseDown={(event) => {
                          event.preventDefault();
                          event.stopPropagation();
                        }}
                        onSelect={() => handleSelectOption(option)}
                        className={cn(
                          "flex w-full items-center gap-2",
                          !isSelected ? "pl-8" : null
                        )}
                      >
                        {isSelected ? <IconCheck className="w-4" /> : null}
                        {option.label}
                      </CommandItem>
                    );
                  })}
                </CommandGroup>
              ) : null}
              {!isLoading && filteredOptions.length === 0 ? (
                <CommandPrimitive.Empty className="select-none rounded-sm px-2 py-3 text-center text-sm">
                  {emptyMessage}
                </CommandPrimitive.Empty>
              ) : null}
            </CommandList>
          </div>
        </div>
      </CommandPrimitive>
    );
  }
);

AutoComplete.displayName = "AutoComplete";
