import { create } from "zustand";
import { AccountPlanTableType } from "../types";

type AccountPlanStore = {
  showSidebar: boolean;
  activeTable: null | AccountPlanTableType;
  toggleSidebar: () => void;
  toggleActiveTable: (type: AccountPlanTableType | null) => void;
};

export const useAccountPlanStore = create<AccountPlanStore>((set) => ({
  showSidebar: false,
  activeTable: null,
  toggleSidebar: () =>
    set((state) => ({
      showSidebar: !state.showSidebar,
    })),
  toggleActiveTable: (type: AccountPlanTableType | null) =>
    set((state) => ({
      activeTable: state.activeTable === type ? null : type,
    })),
}));
