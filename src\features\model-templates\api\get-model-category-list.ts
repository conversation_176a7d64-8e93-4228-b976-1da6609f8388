import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, BaseParams, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { ModelCategory } from "../types";

type ModelCategoryListParams = BaseParams;

export const getModelCategoryList = ({
  params,
}: {
  params?: ModelCategoryListParams;
}): ApiResponse<ModelCategory[]> => {
  return api.get(API_ROUTES.MODEL_TEMPLATES_CATEGORIES, { params });
};

export const getModelCategoryListQueryOptions = (
  params?: ModelCategoryListParams
) => {
  return queryOptions({
    queryKey: [QUERY_KEYS.MODEL_TEMPLATES_CATEGORIES, params],
    queryFn: () => getModelCategoryList({ params }),
  });
};

type UsePromptTemplateCategoriesOptions = {
  params?: ModelCategoryListParams;
  queryConfig?: QueryConfig<typeof getModelCategoryListQueryOptions>;
};

export const useModelCategoryList = ({
  queryConfig,
  params,
}: UsePromptTemplateCategoriesOptions) => {
  const modelCategoryListQuery = useQuery({
    ...getModelCategoryListQueryOptions(params),
    ...queryConfig,
  });

  return {
    ...modelCategoryListQuery,
    modelCategoryList: modelCategoryListQuery.data?.data || [],
  };
};
