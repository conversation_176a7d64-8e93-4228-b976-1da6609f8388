"use client";

import { useForm } from "react-hook-form";
import { z } from "zod";
import { useState } from "react";
import { useSearchParams } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { IconAlertCircle, IconArrowLeft } from "@tabler/icons-react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { isRequestError } from "@/lib/api-client";
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { PATH } from "@/constants/path";
import { PasswordInput } from "@/components/ui/input/password-input";
import { useChangePassword } from "@/features/user-management/api/passwords/change-password";

import { useOrganization } from "../organizations/api/get-organization";
import { PUBLIC_ORGANIZATION_ID } from "../organizations/constants";
import { FormContainer } from "./form-base-layout";

const formSchema = z
  .object({
    password: z.string().min(8, "Password minimum contain 8 characters"),
    password_confirmation: z
      .string()
      .min(8, "Password minimum contain 8 characters"),
  })
  .refine((data) => data.password === data.password_confirmation, {
    message: "Password confirmation doesn't match",
    path: ["password_confirmation"],
  });

type FormSchema = z.infer<typeof formSchema>;

function ResetPasswordForm() {
  const searchParams = useSearchParams();
  const request_code = searchParams.get("request_code");
  const { secondaryColor } = useOrganization({
    organizationId: PUBLIC_ORGANIZATION_ID,
  });

  const form = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    reValidateMode: "onSubmit",
  });
  const changePassword = useChangePassword({});

  const [errorNotif, setErrorNotif] = useState<string>("");
  const [isSuccess, setIsSuccess] = useState(false);

  const onSubmit = async (data: FormSchema) => {
    try {
      if (!request_code) {
        setErrorNotif("Request code not found");
        return;
      }
      setErrorNotif("");
      await changePassword.mutateAsync({ data: { ...data, request_code } });
      setIsSuccess(true);
    } catch (e) {
      if (isRequestError(e)) {
        const errorMessage = e.response?.data.errors[0].message ?? "";

        setErrorNotif(errorMessage ?? "");
      }
    }
  };

  if (isSuccess) {
    return (
      <FormContainer>
        <p className="text-2xl">Reset password</p>
        <div>
          <p>
            Your password has been changed. You can now log in with your new
            password.
          </p>
          <Link href={PATH.LANDING}>
            <Button
              className="mt-6 w-full"
              type="submit"
              style={{
                backgroundColor: secondaryColor,
              }}
            >
              <IconArrowLeft className="mr-2" />
              Back to Login
            </Button>
          </Link>
        </div>
      </FormContainer>
    );
  }

  return (
    <Form {...form}>
      <FormContainer onSubmit={form.handleSubmit(onSubmit)}>
        <div>
          <p className="mb-6 text-center text-xl font-bold">Reset password</p>
        </div>

        <div className="mb-12 grid gap-8">
          {errorNotif && (
            <Alert variant="destructive" className="relative mb-2">
              <IconAlertCircle className="size-4" />
              <AlertTitle className="font-bold">
                Reset Password Failed
              </AlertTitle>
              <AlertDescription>{errorNotif}</AlertDescription>
            </Alert>
          )}

          <div className="grid gap-8">
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <PasswordInput
                      className="h-12"
                      placeholder="Password"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="password_confirmation"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <PasswordInput
                      className="h-12"
                      placeholder="Confirm Password"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="mt-4 grid gap-4">
              <Button
                className="w-full bg-gradient"
                isLoading={changePassword.isPending}
              >
                Submit
              </Button>
            </div>
          </div>
        </div>
      </FormContainer>
    </Form>
  );
}

export default ResetPasswordForm;
