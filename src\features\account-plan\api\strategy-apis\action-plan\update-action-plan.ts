import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import {
  APActionPlan,
  APActionPlanBaseData,
} from "@/features/account-plan/types/strategy-types";

export const updateActionPlan = ({
  accountId,
  id,
  data,
}: {
  id: number;
  accountId: number;
  data?: APActionPlanBaseData;
}): ApiResponse<APActionPlan> => {
  return api.put(
    API_ROUTES.ACCOUNT_PLANS_ACTION_PLAN_DETAIL(accountId, id),
    data
  );
};

type UseUpdateActionPlanOptions = {
  mutationConfig?: MutationConfig<typeof updateActionPlan>;
};

export const useUpdateActionPlan = ({
  mutationConfig,
}: UseUpdateActionPlanOptions) => {
  const queryClient = useQueryClient();
  const { onSuccess, invalidate, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      if (invalidate) {
        await queryClient.invalidateQueries({
          queryKey: [
            QUERY_KEYS.ACCOUNT_PLANS,
            args[1].accountId,
            QUERY_KEYS.ACCOUNT_PLANS_ACTION_PLAN,
          ],
        });
      }

      onSuccess?.(...args);
    },

    ...restConfig,
    mutationFn: updateActionPlan,
  });
};
