import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import {
  APRevenueForecast,
  APRevenueForecastBaseData,
} from "@/features/account-plan/types/revenue-types";
import { QUERY_KEYS } from "@/constants/query-keys";

export const createRevenueForecast = ({
  accountId,
  data,
}: {
  accountId: number;
  data?: APRevenueForecastBaseData;
}): ApiResponse<APRevenueForecast> => {
  return api.post(API_ROUTES.ACCOUNT_PLANS_REVENUE_FORECAST(accountId), data);
};

type UseCreateRevenueForecastOptions = {
  mutationConfig?: MutationConfig<typeof createRevenueForecast>;
};

export const useCreateRevenueForecast = ({
  mutationConfig,
}: UseCreateRevenueForecastOptions) => {
  const queryClient = useQueryClient();
  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      await queryClient.invalidateQueries({
        queryKey: [
          QUERY_KEYS.ACCOUNT_PLANS,
          args[1].accountId,
          QUERY_KEYS.ACCOUNT_PLANS_REVENUE_FORECAST,
        ],
      });

      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: createRevenueForecast,
  });
};
