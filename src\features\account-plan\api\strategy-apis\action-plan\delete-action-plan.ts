import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";

export const deleteActionPlan = ({
  accountId,
  id,
}: {
  id: number;
  accountId: number;
}): ApiResponse => {
  return api.delete(API_ROUTES.ACCOUNT_PLANS_ACTION_PLAN_DETAIL(accountId, id));
};

type UseDeleteActionPlanOptions = {
  mutationConfig?: MutationConfig<typeof deleteActionPlan>;
};

export const useDeleteActionPlan = ({
  mutationConfig,
}: UseDeleteActionPlanOptions) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      await queryClient.invalidateQueries({
        queryKey: [
          QUERY_KEYS.ACCOUNT_PLANS,
          args[1].accountId,
          QUERY_KEYS.ACCOUNT_PLANS_ACTION_PLAN,
        ],
      });

      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: deleteActionPlan,
  });
};
