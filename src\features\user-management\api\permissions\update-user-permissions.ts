import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { UserPermissions, UserPermissionsPayload } from "../../types";

export const updateUserPermissions = ({
  userId,
  data,
}: {
  userId: number;
  data?: UserPermissionsPayload;
}): ApiResponse<UserPermissions> => {
  return api.put(
    `${API_ROUTES.USER_MANAGEMENTS_USER_PERMISSIONS}/${userId}`,
    data
  );
};

type UseUpdateUserPermissionsOptions = {
  mutationConfig?: MutationConfig<typeof updateUserPermissions>;
};

export const useUpdateUserPermissions = ({
  mutationConfig,
}: UseUpdateUserPermissionsOptions) => {
  const { onSuccess, invalidate, ...restConfig } = mutationConfig || {};
  const queryClient = useQueryClient();

  return useMutation({
    onSuccess: (...args) => {
      if (invalidate) {
        queryClient.invalidateQueries({
          queryKey: [QUERY_KEYS.USER_PERMISSIONS],
        });
      }

      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: updateUserPermissions,
  });
};
