"use client";

import { Select } from "@radix-ui/react-select";
import {
  IconArchiveFilled,
  IconPlus,
  IconRestore,
  IconTrash,
  IconZoom,
} from "@tabler/icons-react";
import { ColDef, RowSelectedEvent } from "ag-grid-community";
import Link from "next/link";
import {
  parseAsString,
  parseAsStringEnum,
  useQueryState,
} from "next-usequerystate";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { PATH } from "@/constants/path";
import { useUserManagementList } from "@/features/user-management/api/get-user-management-list";
import { useScalingDimension } from "@/lib/hooks/use-scaling-dimension";
import { UserManagement } from "@/features/user-management/types";
import { Grid } from "@/components/ui/grid";
import { formatDate } from "@/lib/utils";
import {
  GridA<PERSON>,
  GridDeleteButton,
  GridEditButton,
} from "@/components/ui/grid/grid-actions";
import { useRolesPermissions } from "@/features/user-management/hooks/use-role-permissions";
import { useIsHydrated } from "@/lib/hooks/use-hydrated";
import { UserRole, UserStatus } from "@/features/auth/types/user";
import { useAuth } from "@/features/auth/api/get-auth";
import { useDeleteUser } from "@/features/auth/api/user/delete-user";
import { TableTooltip } from "@/features/account-plan/components/tables/table-tooltip";
import { useUpdateUser } from "@/features/auth/api/user/update-user";
import {
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { getRoleAlias } from "@/features/user-management/constants";
import { useOrganizationOptions } from "@/features/user-management/hooks/use-organization-list";
import { OrganizationListTable } from "@/features/organizations/components/organization-list-table";
import { useLogout } from "@/features/auth/api/logout";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import useOrganizationFilterQueryState from "@/features/organizations/hooks/use-organization-filter-query-state";

type UserManagementTableData = UserManagement;

const statusOptions = [
  {
    label: "Active Users",
    value: UserStatus.ACTIVE,
  },
  {
    label: "Archived Users",
    value: UserStatus.ARCHIVED,
  },
];

function UserManagementPage() {
  const { getWidth, getHeight } = useScalingDimension();
  const { isAbleToInvite, checkIsLowerRole, isAbleToManageOtherOrganizations } =
    useRolesPermissions();
  const { isHydrated } = useIsHydrated();
  const { auth, isOwner, isOwnAccount } = useAuth({});
  const logout = useLogout();

  const { organizationOptions } = useOrganizationOptions();

  const [statusFilter, setStatusFilter] = useQueryState(
    "status",
    parseAsStringEnum<UserStatus>(Object.values(UserStatus)).withDefault(
      UserStatus.ACTIVE
    )
  );
  const [organizationFilter, setOrganizationFilter] =
    useOrganizationFilterQueryState();
  const [search, setSearch] = useQueryState(
    "search",
    parseAsString.withDefault("")
  );

  const [userManagementData, setUserManagementData] = useState<
    UserManagementTableData[]
  >([]);
  const [selectedRows, setSelectedRows] = useState<UserManagementTableData[]>(
    []
  );
  const [isLoadingDeleteMutliple, setLoadingDeleteMutliple] = useState(false);
  const [isLoadingArchiveutliple, setLoadingArchiveMutliple] = useState(false);

  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isArchiveDialogOpen, setIsArchiveDialogOpen] = useState(false);

  const activeOrganizationId = Number(organizationFilter);
  const {
    userManagementList,
    isLastRemainingSuperAdmin,
    isFetching: isFetchingUserManagement,
  } = useUserManagementList({
    params: {
      disable_pagination: true,
      status: statusFilter,
      ...(organizationFilter &&
        isAbleToManageOtherOrganizations && {
          organization_id: activeOrganizationId,
        }),
      ...(!!search && { search }),
    },
  });
  const deleteUser = useDeleteUser({});
  const updateUser = useUpdateUser({
    mutationConfig: {
      invalidate: true,
    },
  });

  const getMutateConfig = useCallback(
    (userId?: number, userRole?: UserRole) => {
      if (!userId || !userRole)
        return {
          isSelectable: false,
          isAbleToEdit: false,
          isAbleToDelete: false,
        };

      const isSelf = isOwnAccount(userId);
      const isOwner = userRole === UserRole.OWNER;
      const canModifyOther = checkIsLowerRole(userRole);

      return {
        isSelectable: canModifyOther,
        isAbleToEdit: isSelf || canModifyOther,
        isAbleToDelete:
          (isSelf && isOwner && isLastRemainingSuperAdmin) ||
          (isSelf && !isOwner) ||
          canModifyOther,
      };
    },
    [checkIsLowerRole, isOwnAccount, isLastRemainingSuperAdmin]
  );

  const isOwnAccountSelected = useMemo(
    () => selectedRows.some((v) => isOwnAccount(v.user_id)),
    [isOwnAccount, selectedRows]
  );

  const onDeleteUser = useCallback(
    async (userId: number) => {
      try {
        await deleteUser.mutateAsync({
          userId,
          params: {
            ...(isOwner && { organization_id: activeOrganizationId }),
          },
        });

        toast("User has been deleted");

        if (isOwnAccount(userId)) {
          logout();
        }
      } catch {
        toast.error("An error occured upon user deletion");
      }
    },
    [activeOrganizationId, deleteUser, isOwnAccount, isOwner, logout]
  );

  const onDeleteMultipleUsers = async () => {
    try {
      setLoadingDeleteMutliple(true);

      const promises = selectedRows.map(async ({ user_id }) => {
        return deleteUser.mutateAsync({
          userId: user_id,
          params: {
            ...(isOwner && { organization_id: activeOrganizationId }),
          },
        });
      });

      await Promise.all(promises);

      toast("All selected users have been deleted");
      setIsDeleteDialogOpen(false);

      if (isOwnAccountSelected) {
        logout();
      }
    } catch {
      toast("An error occured upon user deletion");
    } finally {
      setLoadingDeleteMutliple(false);
    }
  };

  const onArchiveMultipleUsers = async () => {
    try {
      setLoadingArchiveMutliple(true);

      const promises = selectedRows.map(async ({ user_id }) => {
        return updateUser.mutateAsync({
          userId: user_id,
          data: {
            status:
              statusFilter === UserStatus.ARCHIVED
                ? UserStatus.ACTIVE
                : UserStatus.ARCHIVED,
            ...(isOwner && { organization_id: activeOrganizationId }),
          },
        });
      });

      await Promise.all(promises);
      setIsArchiveDialogOpen(false);

      if (isOwnAccountSelected) {
        logout();
      }

      toast(
        `All selected users have been ${statusFilter === UserStatus.ARCHIVED ? "activated" : "archived"}`
      );
    } catch {
      toast("An error occured while archiving users");
    } finally {
      setLoadingArchiveMutliple(false);
    }
  };

  const handleRowSelected = (
    event: RowSelectedEvent<UserManagementTableData>
  ) => {
    setSelectedRows(event.api.getSelectedRows());
  };

  const userManagementColumns: ColDef<UserManagementTableData>[] =
    useMemo(() => {
      const columns: (ColDef<UserManagementTableData> & { show?: boolean })[] =
        [
          {
            field: "organization_unique_id",
            headerName: "Organization ID",
            maxWidth: getWidth(225),
          },
          {
            field: "organization_identifier_id",
            headerName: "Employee ID",
            maxWidth: getWidth(200),
            headerComponent: TableTooltip,
            headerComponentParams: {
              description:
                "Unique identifier assigned to each employee which is used for tracking and management purposes.",
            },
          },
          {
            field: "first_name",
            headerName: "First Name",
            maxWidth: getWidth(200),
            valueFormatter: (params) => params.data?.first_name ?? "-",
            headerComponent: TableTooltip,
            headerComponentParams: {
              description:
                "Name of the Account Manager responsible for this account",
            },
          },
          {
            field: "last_name",
            headerName: "Last Name",
            maxWidth: getWidth(200),
            valueFormatter: (params) => params.data?.last_name ?? "-",
          },
          {
            field: "role.name",
            headerName: "Permissions",
            maxWidth: getWidth(200),
            valueFormatter: (params) => getRoleAlias(params.data?.role?.name),
          },
          {
            field: "email",
            headerName: "Email",
            valueFormatter: (params) => params.data?.email ?? "-",
          },
          {
            field: "last_login_at",
            headerName: "Last Login",
            maxWidth: getWidth(175),
            valueFormatter: (params) =>
              formatDate(params.data?.last_login_at, "DD/MM/YYYY"),
            headerComponent: TableTooltip,
            headerComponentParams: {
              description:
                "Date of the last system access. Displays the last time the account manager logged into the system.",
            },
          },
          {
            field: "user_id",
            headerName: "Action",
            maxWidth: getWidth(125),
            sortable: false,
            cellRenderer: (
              ...args: {
                data: { role: { name: UserRole } };
                value: number;
              }[]
            ) => {
              const { isAbleToEdit, isAbleToDelete } = getMutateConfig(
                args[0].value,
                args[0].data.role.name
              );

              if (isAbleToEdit) {
                return (
                  <GridActions>
                    <GridEditButton
                      href={
                        PATH.DASHBOARD_USER_MANAGEMENT_PERMISSION(
                          args[0].value
                        ) +
                        (isOwner
                          ? `?organization_id=${activeOrganizationId}`
                          : "")
                      }
                    />

                    {isAbleToDelete && (
                      <GridDeleteButton
                        onDelete={() => onDeleteUser(args[0].value)}
                        itemName="User"
                        isLoading={deleteUser.isPending}
                        description={
                          <>
                            Are you sure want to delete{" "}
                            {isOwnAccount(args[0].value) ? (
                              <>
                                your own account?
                                <br />
                                You will be logged out after this action.
                              </>
                            ) : (
                              "this account?"
                            )}
                          </>
                        }
                      />
                    )}
                  </GridActions>
                );
              } else {
                return null;
              }
            },
          },
        ];

      return columns.filter((v) => v.show === undefined || v.show);
    }, [
      getWidth,
      getMutateConfig,
      deleteUser,
      onDeleteUser,
      isOwnAccount,
      activeOrganizationId,
      isOwner,
    ]);

  useEffect(() => {
    if (!userManagementList) return;

    const newUserManagementRows = userManagementList?.map((v, idx) => ({
      idx,
      ...v,
    }));

    setUserManagementData(newUserManagementRows);
  }, [userManagementList, setUserManagementData]);

  useEffect(() => {
    if (auth && !organizationFilter) {
      setOrganizationFilter(auth.user.organization.name);
    }
  }, [auth, organizationFilter, setOrganizationFilter]);

  if (!isHydrated) return null;

  return (
    <div className="w-full">
      <div className="mb-8 flex items-start justify-between">
        <div className="grid gap-res-x-base">
          <h1 className="text-3xl font-bold text-primary-500">
            User Management
          </h1>
          <p className="text-2xl font-semibold text-secondary-500">
            {userManagementList.length ?? "-"} accounts
          </p>
        </div>

        <div className="flex items-center gap-res-x-base">
          {isAbleToInvite && (
            <Link href={PATH.DASHBOARD_USER_MANAGEMENT_INVITE}>
              <Button className="bg-gradient text-white">
                <IconPlus className="mr-2 size-icon-res-base" /> Add Member
              </Button>
            </Link>
          )}

          <section className="flex w-full justify-between">
            <div className="flex w-fit items-center gap-2">
              <Input
                placeholder="Search User"
                startIcon={IconZoom}
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="rounded-lg"
              />

              <Dialog
                open={isDeleteDialogOpen}
                onOpenChange={setIsDeleteDialogOpen}
              >
                <TooltipProvider>
                  <Tooltip>
                    <DialogTrigger>
                      <TooltipTrigger>
                        <Button
                          variant="destructive"
                          disabled={selectedRows.length === 0}
                        >
                          <IconTrash className="size-res-x-base" />
                        </Button>
                      </TooltipTrigger>
                    </DialogTrigger>
                    <TooltipContent>
                      {selectedRows.length > 0
                        ? "Delete all selected users"
                        : "Please select users first"}
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <DialogContent>
                  <DialogClose />
                  <DialogTitle>Delete Multiple Users</DialogTitle>
                  <DialogDescription>
                    Are you sure want to delete all selected{" "}
                    {isOwnAccountSelected ? (
                      <>
                        accounts (including your own account)? You will be
                        logged out after this action.
                      </>
                    ) : (
                      "accounts?"
                    )}
                  </DialogDescription>

                  <DialogFooter>
                    <Button
                      isLoading={isLoadingDeleteMutliple}
                      onClick={onDeleteMultipleUsers}
                      variant="destructive"
                    >
                      Delete Users
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>

              <Dialog
                open={isArchiveDialogOpen}
                onOpenChange={setIsArchiveDialogOpen}
              >
                <TooltipProvider>
                  <Tooltip>
                    <DialogTrigger>
                      <TooltipTrigger>
                        <Button
                          variant="outline"
                          disabled={selectedRows.length === 0}
                        >
                          {statusFilter === UserStatus.ACTIVE ? (
                            <IconArchiveFilled className="size-res-x-base text-primary-500" />
                          ) : (
                            <IconRestore className="size-res-x-base text-primary-500" />
                          )}
                        </Button>
                      </TooltipTrigger>
                    </DialogTrigger>
                    <TooltipContent>
                      {selectedRows.length > 0
                        ? statusFilter === UserStatus.ACTIVE
                          ? "Archive all selected users"
                          : "Activate all selected users"
                        : "Please select users first"}
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <DialogContent>
                  <DialogClose />
                  <DialogTitle>
                    {statusFilter === UserStatus.ACTIVE
                      ? "Archive"
                      : "Activate"}{" "}
                    Multiple Users
                  </DialogTitle>
                  <DialogDescription>
                    Are you sure want to{" "}
                    {statusFilter === UserStatus.ACTIVE
                      ? "archive"
                      : "activate"}{" "}
                    all selected{" "}
                    {isOwnAccountSelected ? (
                      <>
                        accounts (including your own account)? You will be
                        logged out after this action.
                      </>
                    ) : (
                      "accounts?"
                    )}
                  </DialogDescription>

                  <DialogFooter>
                    <Button
                      isLoading={isLoadingArchiveutliple}
                      onClick={onArchiveMultipleUsers}
                    >
                      {statusFilter === UserStatus.ACTIVE
                        ? "Archive"
                        : "Activate"}{" "}
                      Users
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </section>
        </div>
      </div>

      <section className="mt-8 rounded-md bg-white p-4">
        <div className="mb-res-x-sm flex items-center justify-between gap-res-x-base">
          <Select
            onValueChange={(value: UserStatus) => {
              setSelectedRows([]);
              setStatusFilter(value);
            }}
            value={statusFilter ?? ""}
          >
            <SelectTrigger className="w-fit">
              <b className="mr-res-x-sm">Status:</b>{" "}
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              {statusOptions.map((v, idx) => (
                <SelectItem
                  key={idx}
                  value={v.value}
                  className="cursor-pointer"
                >
                  {v.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {isAbleToManageOtherOrganizations && (
            <div className="flex items-center gap-res-x-xs">
              <OrganizationListTable />
              <Select
                onValueChange={(value: UserStatus) => {
                  setSelectedRows([]);
                  setOrganizationFilter(value);
                }}
                value={organizationFilter ?? ""}
              >
                <SelectTrigger className="w-fit">
                  <b className="mr-res-x-sm">Organization:</b>{" "}
                  <SelectValue placeholder="Select organization" />
                </SelectTrigger>
                <SelectContent>
                  {organizationOptions.map((v, idx) => (
                    <SelectItem
                      key={idx}
                      value={v.value ?? ""}
                      className="cursor-pointer items-center justify-between pr-8"
                    >
                      {v.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
        </div>
        <Grid<UserManagementTableData>
          suppressCellFocus
          headerHeight={getHeight(80)}
          rowHeight={getHeight(60)}
          columnDefs={userManagementColumns}
          rowData={userManagementData}
          height="70vh"
          defaultColDef={{
            flex: 1,
            wrapText: true,
            autoHeight: true,
            cellEditorParams: {
              maxLength: 9999,
            },
          }}
          loading={isFetchingUserManagement}
          overlayNoRowsTemplate="No user found"
          rowSelection={{
            mode: "multiRow",
            hideDisabledCheckboxes: true,
            isRowSelectable: (node) => {
              return getMutateConfig(node.data?.user_id, node.data?.role?.name)
                .isSelectable;
            },
          }}
          autoSizeStrategy={{
            type: "fitGridWidth",
          }}
          onRowSelected={handleRowSelected}
        />
      </section>
    </div>
  );
}

export default UserManagementPage;
