import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { toast } from "sonner";

export const deleteModel = ({ modelId }: { modelId: number }): ApiResponse => {
  return api.delete(`${API_ROUTES.MODEL_TEMPLATES}/${modelId}`);
};

type UseDeleteModelOptions = {
  mutationConfig?: MutationConfig<typeof deleteModel>;
};

export const useDeleteModel = ({ mutationConfig }: UseDeleteModelOptions) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      await queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.MODEL_TEMPLATES],
      });

      toast("The selected model has been successfully removed");

      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: deleteModel,
  });
};
