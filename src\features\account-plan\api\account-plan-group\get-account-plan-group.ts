import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { AccountPlanGroupsData } from "../../types";

export const getAccountPlanGroups = ({
  accountGroupId,
}: {
  accountGroupId?: number;
}): ApiResponse<AccountPlanGroupsData> => {
  return api.get(API_ROUTES.ACCOUNT_PLAN_GROUPS_DETAIL(accountGroupId));
};

export const getAccountPlanGroupsQueryOptions = (accountGroupId?: number) => {
  return queryOptions({
    queryKey: [QUERY_KEYS.ACCOUNT_PLANS_GROUPS, accountGroupId],
    queryFn: () => getAccountPlanGroups({ accountGroupId }),
    enabled: !!accountGroupId,
  });
};

type UseAccountPlanGroupsOptions = {
  accountGroupId?: number;
  queryConfig?: QueryConfig<typeof getAccountPlanGroups>;
  options?: Partial<ReturnType<typeof getAccountPlanGroupsQueryOptions>>;
};

export const useAccountPlanGroups = ({
  accountGroupId,
  queryConfig,
  options,
}: UseAccountPlanGroupsOptions) => {
  const accountPlanGroupsQuery = useQuery({
    ...getAccountPlanGroupsQueryOptions(accountGroupId),
    ...queryConfig,
    ...options,
  });

  return {
    ...accountPlanGroupsQuery,
    accountPlanGroups: accountPlanGroupsQuery.data?.data,
  };
};
