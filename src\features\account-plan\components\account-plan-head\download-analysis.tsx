"use client";

import React, { useEffect } from "react";
import { Document, Font, Page, View, pdf } from "@react-pdf/renderer";
import { saveAs } from "file-saver";

import { useMissingInformationList } from "../../api/strategy-apis/missing-information/get-missing-information-list";
import { useParams } from "next/navigation";
import { useTargetedPerceptionDevelopmentList } from "../../api/strategy-apis/targeted-perception-development/get-targeted-perception-development-list";
import {
  Tooltip,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { getFullName } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { IconDownload } from "@tabler/icons-react";
import { getCurrencyAbbreviations } from "@/constants/currencies";

import { useActionPlanList } from "../../api/strategy-apis/action-plan/get-action-plan-list";
import { useTopActionList } from "../../api/strategy-apis/top-action/get-top-action-list";
import { useStakeholderMappingList } from "../../api/position-apis/stakeholder-mapping/get-stakeholder-mapping-list";
import { useWalletShareList } from "../../api/position-apis/wallet-share/get-wallet-share-list";
import { useCircumstantialAnalysisList } from "../../api/position-apis/circumstantial-analysis/get-circumstantial-analysis-list";
import { useSvotList } from "../../api/position-apis/svot/get-svot-list";
import { useInsightAndPerspectiveList } from "../../api/position-apis/insight-and-perspective/get-insight-and-perspective-list";
import { useHistoricRevenueList } from "../../api/revenue-apis/historic-revenue/get-historic-revenue-list";
import { useCurrentRevenueList } from "../../api/revenue-apis/current-revenue/get-current-revenue-list";
import { useCurrentOpportunityList } from "../../api/revenue-apis/current-opportunity/get-current-opportunity-list";
import { usePotentialOpportunityList } from "../../api/revenue-apis/potential-opportunity/get-potential-opportunity-list";
import { useRevenueForecastList } from "../../api/revenue-apis/revenue-forecast/get-revenue-forecast-list";
import {
  APCircumstantialAnalysis,
  APInsightAndPerspective,
  APStakeholderMapping,
  APSvot,
  APWalletShare,
} from "../../types/position-types";
import { StakeholderPDFTable } from "./pdf-tables/position-tables/stakeholder-pdf-table";
import { SvotPDFTable } from "./pdf-tables/position-tables/svot-pdf-table";
import { WalletSharePDFTable } from "./pdf-tables/position-tables/wallet-share-pdf-table";
import { CircumstantialAnalysisPDFTable } from "./pdf-tables/position-tables/circumstantial-analysis-pdf-table";
import {
  pdfStyles,
  PdfMetadata,
  PdfMetadataRow,
  PdfHeading,
} from "./pdf-tables";
import { useAccountPlan } from "../../api/get-account-plan";
import {
  APCurrentOpportunity,
  APCurrentRevenue,
  APHistoricRevenue,
  APPotentialOpportunity,
  APRevenueForecast,
} from "../../types/revenue-types";
import {
  APActionPlan,
  APClientMeetingSchedule,
  APMissingInformation,
  APTargetedPerceptionDevelopment,
  APTopAction,
} from "../../types/strategy-types";
import { InsightsAndPerspectivesPDFTable } from "./pdf-tables/position-tables/insight-and-perspective-pdf-table";
import { HistoricRevenuePDFTable } from "./pdf-tables/revenue-tables/historic-revenue-pdf-table";
import { CurrentRevenuePDFTable } from "./pdf-tables/revenue-tables/current-revenue-pdf-table";
import { CurrentOpportunityPDFTable } from "./pdf-tables/revenue-tables/current-opportunity-pdf-table";
import { PotentialOpportunityPDFTable } from "./pdf-tables/revenue-tables/potential-opportunity-pdf-table";
import { RevenueForecastPDFTable } from "./pdf-tables/revenue-tables/revenue-forecast-pdf-table";
import { InformationNeededPDFTable } from "./pdf-tables/strategy-tables/information-needed-pdf-table";
import { StrategicPerceptionPDFTable } from "./pdf-tables/strategy-tables/strategic-perception-pdf-table";
import { StrategicConsiderationPDFTable } from "./pdf-tables/strategy-tables/strategic-consideration-pdf-table";
import { StrategicActionPDFTable } from "./pdf-tables/strategy-tables/strategic-action-pdf-table";
import { useClientMeetingScheduleList } from "../../api/strategy-apis/client-meeting-schedule/get-client-meeting-schedule-list";
import { MeetingSchedulePDFTable } from "./pdf-tables/strategy-tables/meeting-schedule-pdf-table";
import { useAccountPlanGroups } from "../../api/account-plan-group/get-account-plan-group";
import { AccountPlanData, AccountPlanGroupsData } from "../../types";
import { walletShareOptions } from "../tables/position-tables/wallet-share-table";

export type AccountPlanAnalysisPDFProps = {
  accountPlanGroup?: AccountPlanGroupsData;
  accountPlan?: AccountPlanData;
  stakeholderMappingList: APStakeholderMapping[];
  walletShareList: APWalletShare[];
  circumstantialAnalysisList: APCircumstantialAnalysis[];
  svotList: APSvot[];
  insightAndPerspectiveList: APInsightAndPerspective[];
  historicRevenueList: APHistoricRevenue[];
  currentRevenueList: APCurrentRevenue[];
  currentOpportunityList: APCurrentOpportunity[];
  potentialOpportunityList: APPotentialOpportunity[];
  revenueForecastList: APRevenueForecast[];
  missingInformationList: APMissingInformation[];
  targetedPerceptionDevelopmentList: APTargetedPerceptionDevelopment[];
  actionPlanList: APActionPlan[];
  topActionList: APTopAction[];
  clientMeetingScheduleList: APClientMeetingSchedule[];
};

const AccountPlanAnalysisPDF = ({
  accountPlan,
  accountPlanGroup,
  ...props
}: AccountPlanAnalysisPDFProps) => {
  const currency = getCurrencyAbbreviations(accountPlanGroup?.currency);

  return (
    <Document>
      <Page size="A4" style={pdfStyles.page}>
        <View style={{ marginBottom: 20 }}>
          <PdfHeading>
            {accountPlanGroup?.company} Account Plan Analysis (
            {accountPlan?.version})
          </PdfHeading>
          <PdfMetadataRow>
            <PdfMetadata title="Account ID">
              {accountPlanGroup?.account_plan_unique_id ?? "-"}
            </PdfMetadata>
            <PdfMetadata title="Industry">
              {accountPlanGroup?.industry?.name ?? "-"}
            </PdfMetadata>
          </PdfMetadataRow>

          <PdfMetadataRow>
            <PdfMetadata title="Company">
              {accountPlanGroup?.company ?? "-"}
            </PdfMetadata>
            <PdfMetadata title="Currency">
              {accountPlanGroup?.currency ?? "-"}
            </PdfMetadata>
          </PdfMetadataRow>

          <PdfMetadataRow>
            <PdfMetadata title="Account Addressable Area">
              {accountPlanGroup?.account_addressable_area ?? "-"}
            </PdfMetadata>

            <PdfMetadata title="Account Owner">
              {getFullName(
                accountPlan?.owner_user?.first_name,
                accountPlan?.owner_user?.last_name
              )}
            </PdfMetadata>
          </PdfMetadataRow>
        </View>

        <PdfHeading>Position Tables</PdfHeading>
        <StakeholderPDFTable data={props.stakeholderMappingList} />
        <WalletSharePDFTable data={props.walletShareList} />
        <CircumstantialAnalysisPDFTable
          data={props.circumstantialAnalysisList}
        />
        <SvotPDFTable data={props.svotList} />
        <InsightsAndPerspectivesPDFTable
          data={props.insightAndPerspectiveList}
        />

        <PdfHeading>Revenue Tables</PdfHeading>
        <HistoricRevenuePDFTable
          data={props.historicRevenueList}
          currency={currency}
        />
        <CurrentRevenuePDFTable
          data={props.currentRevenueList}
          currency={currency}
        />
        <CurrentOpportunityPDFTable
          data={props.currentOpportunityList}
          currency={currency}
        />
        <PotentialOpportunityPDFTable
          data={props.potentialOpportunityList}
          currency={currency}
        />
        <RevenueForecastPDFTable data={props.revenueForecastList} />

        <PdfHeading>Strategy Tables</PdfHeading>
        <InformationNeededPDFTable data={props.missingInformationList} />
        <StrategicPerceptionPDFTable
          data={props.targetedPerceptionDevelopmentList}
        />
        <StrategicConsiderationPDFTable data={props.actionPlanList} />
        <StrategicActionPDFTable data={props.topActionList} />
        <MeetingSchedulePDFTable data={props.clientMeetingScheduleList} />
      </Page>
    </Document>
  );
};

export const DownloadAnalysisButton = ({
  accountPlan,
  accountPlanGroups,
}: {
  accountPlan?: AccountPlanData;
  accountPlanGroups?: AccountPlanGroupsData;
}) => {
  const { id } = useParams<{ id: string }>();
  const accountId = parseInt(id);

  const { stakeholderMappingList } = useStakeholderMappingList({ accountId });
  const { walletShareList } = useWalletShareList({ accountId });
  const { circumstantialAnalysisList } = useCircumstantialAnalysisList({
    accountId,
  });
  const { svotList } = useSvotList({ accountId });
  const { insightAndPerspectiveList } = useInsightAndPerspectiveList({
    accountId,
  });

  const { historicRevenueList } = useHistoricRevenueList({ accountId });
  const { currentRevenueList } = useCurrentRevenueList({ accountId });
  const { currentOpportunityList } = useCurrentOpportunityList({ accountId });
  const { potentialOpportunityList } = usePotentialOpportunityList({
    accountId,
  });
  const { revenueForecastList } = useRevenueForecastList({ accountId });

  const { missingInformationList } = useMissingInformationList({ accountId });
  const { targetedPerceptionDevelopmentList } =
    useTargetedPerceptionDevelopmentList({ accountId });
  const { actionPlanList } = useActionPlanList({ accountId });
  const { topActionList } = useTopActionList({ accountId });
  const { clientMeetingScheduleList } = useClientMeetingScheduleList({
    accountId,
  });

  useEffect(() => {
    Font.register({
      family: "Inter",
      fonts: [
        {
          src: "https://cdn.jsdelivr.net/npm/inter-font@3.19.0/ttf/Inter-Regular.ttf",
        },
        {
          src: "https://cdn.jsdelivr.net/npm/inter-font@3.19.0/ttf/Inter-Medium.ttf",
          fontWeight: 500,
        },
        {
          src: "https://cdn.jsdelivr.net/npm/inter-font@3.19.0/ttf/Inter-SemiBold.ttf",
          fontWeight: 600,
        },
        {
          src: "https://cdn.jsdelivr.net/npm/inter-font@3.19.0/ttf/Inter-Bold.ttf",
          fontWeight: 700,
        },
      ],
    });
  }, []);

  const onDownloadPdf = async () => {
    const pdfProps = {
      accountPlanGroup: accountPlanGroups,
      accountPlan,
      stakeholderMappingList: stakeholderMappingList ?? [],
      walletShareList:
        walletShareList?.sort(
          (a, b) =>
            walletShareOptions[a.item_type].order -
            walletShareOptions[b.item_type].order
        ) ?? [],
      circumstantialAnalysisList: circumstantialAnalysisList ?? [],
      svotList: svotList ?? [],
      insightAndPerspectiveList: insightAndPerspectiveList ?? [],
      historicRevenueList: historicRevenueList ?? [],
      currentRevenueList: currentRevenueList ?? [],
      currentOpportunityList: currentOpportunityList ?? [],
      potentialOpportunityList: potentialOpportunityList ?? [],
      revenueForecastList: revenueForecastList ?? [],
      missingInformationList: missingInformationList ?? [],
      targetedPerceptionDevelopmentList:
        targetedPerceptionDevelopmentList ?? [],
      actionPlanList: actionPlanList ?? [],
      topActionList: topActionList ?? [],
      clientMeetingScheduleList: clientMeetingScheduleList ?? [],
    };

    const blob = await pdf(<AccountPlanAnalysisPDF {...pdfProps} />).toBlob();
    saveAs(
      blob,
      `${accountPlanGroups?.company ?? "Unknown Company"} Account Plan (${accountPlan?.version}).pdf`
    );
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            className="items-center px-4"
            onClick={onDownloadPdf}
            // disabled={!accountPlan?.generated_analysis}
          >
            <IconDownload className="mr-[0.3vw] size-res-x-sm" /> Download
            Account Plan
          </Button>
        </TooltipTrigger>
      </Tooltip>
    </TooltipProvider>
  );
};
