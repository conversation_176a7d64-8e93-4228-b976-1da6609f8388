"use client";

import { Inter, Poppins } from "next/font/google";

import { cn } from "@/lib/utils";
import React from "react";

const poppins = Poppins({
  subsets: ["latin"],
  weight: ["200", "300", "400", "500", "600", "700"],
  variable: "--font-poppins",
});

const inter = Inter({ subsets: ["latin"], variable: "--font-inter" });

export default function RootContainer({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={cn(
          poppins.variable,
          inter.variable,
          "flex h-screen flex-col font-inter"
        )}
      >
        {children}
      </body>
    </html>
  );
}
