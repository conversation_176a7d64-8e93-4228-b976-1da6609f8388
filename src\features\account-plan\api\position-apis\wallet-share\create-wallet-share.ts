import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import {
  APWalletShare,
  APWalletShareBaseData,
} from "@/features/account-plan/types/position-types";
import { QUERY_KEYS } from "@/constants/query-keys";

export const createWalletShare = ({
  accountId,
  data,
}: {
  accountId: number;
  data?: APWalletShareBaseData;
}): ApiResponse<APWalletShare> => {
  return api.post(API_ROUTES.ACCOUNT_PLANS_WALLET_SHARE(accountId), data);
};

type UseCreateWalletShareOptions = {
  mutationConfig?: MutationConfig<typeof createWalletShare>;
};

export const useCreateWalletShare = ({
  mutationConfig,
}: UseCreateWalletShareOptions) => {
  const queryClient = useQueryClient();
  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      await queryClient.invalidateQueries({
        queryKey: [
          QUERY_KEYS.ACCOUNT_PLANS,
          args[1].accountId,
          QUERY_KEYS.ACCOUNT_PLANS_WALLET_SHARE,
        ],
      });

      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: createWalletShare,
  });
};
