import * as React from "react";
import mergeRefs from "merge-refs";
import bytes from "bytes";

import { Input } from "@/components/ui/input";
import { useUploadData } from "@/features/upload/api/upload-with-presigned";
import { Button } from "@/components/ui/button";
import { IconCircleX, IconFileText, IconPaperclip } from "@tabler/icons-react";
import { cn } from "@/lib/utils";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { toast } from "sonner";
import _ from "lodash";
import Link from "next/link";
import { Spinner } from "@/components/ui/spinner";
import Image from "next/image";
import AttachIcon from "@/assets/attach.png";

type FileValue = {
  filename?: string;
  url?: string;
} | null;

export interface UploadAttachmentProps
  extends Omit<
    React.InputHTMLAttributes<HTMLInputElement>,
    "value" | "onChange"
  > {
  className?: React.HTMLAttributes<HTMLDivElement>["className"];
  value?: FileValue;
  onChange?: (value: FileValue, file: File) => void;
  onRemove?: () => void;
}

const UploadAttachment = React.forwardRef<
  HTMLInputElement,
  UploadAttachmentProps
>(({ className, onChange, onRemove, value, ...props }, ref) => {
  const uploadData = useUploadData({});
  const fileRef = React.useRef<HTMLInputElement>(null);

  const [placeholderFile, setPlaceholderFile] = React.useState<File | null>(
    null
  );

  const onUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files) return;

    const file = e.target.files[0];

    try {
      setPlaceholderFile(file);

      const res = await uploadData.mutateAsync({
        data: {
          file,
        },
      });

      const fileValue = {
        url: res.data.download_url,
        filename: file.name,
      };

      onChange?.(fileValue, file);
    } catch (_) {
      toast("An error occured while uploading the file");
    } finally {
      setPlaceholderFile(null);
    }
  };

  return (
    <div className={cn("flex items-start", className)}>
      <Input
        className="hidden"
        ref={mergeRefs(ref, fileRef)}
        {...props}
        type="file"
        onChange={onUpload}
      />
      <TooltipProvider>
        <Tooltip delayDuration={150}>
          <TooltipTrigger asChild>
            <Button
              type="button"
              variant="icon"
              size="icon"
              className="w-fit"
              onClick={() => {
                fileRef.current?.click();
              }}
            >
              <div className="mt-2 flex items-center justify-center gap-1">
                <Image className="h-8 w-8" src={AttachIcon} alt="Upload" />
                <div>Attach Files</div>
              </div>
            </Button>
          </TooltipTrigger>
          <TooltipContent>Attachment</TooltipContent>
        </Tooltip>
      </TooltipProvider>
      {(placeholderFile || !!value?.url) && (
        <div className="relative ml-2 items-center rounded-md border border-neutral-200 bg-white p-2">
          <Link href={value?.url ?? ""} className="flex w-fit gap-1">
            {uploadData.isPending ? (
              <Spinner className="h-5 w-5" />
            ) : (
              <IconFileText className="h-5 w-5 shrink-0" />
            )}
            <div className="">
              <p className="overflow-hidden break-all text-xs font-normal">
                {_.truncate(placeholderFile?.name || value?.filename, {
                  length: 20,
                  separator: ".",
                })}
              </p>
              <p className="text-xs text-neutral-500">
                {placeholderFile ? bytes(placeholderFile.size) : "Attachment"}
              </p>
            </div>
          </Link>
          {!uploadData.isPending && (
            <IconCircleX
              className="absolute -right-2 -top-1 h-4 w-4 shrink-0 cursor-pointer text-red-500"
              onClick={(e) => {
                e.preventDefault();
                onRemove?.();
              }}
            />
          )}
        </div>
      )}
    </div>
  );
});
UploadAttachment.displayName = "UploadAttachment";

export { UploadAttachment };
