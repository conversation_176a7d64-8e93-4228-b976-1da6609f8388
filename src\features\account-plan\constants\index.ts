import { AccountPlanTableType } from "../types";

export const accountPlanTableTypeList = [
  {
    name: "Stakeholder Mapping",
    value: AccountPlanTableType.STAKEHOLDER_MAPPING,
  },
  { name: "Wallet Size Analysis", value: AccountPlanTableType.WALLET_SHARE },
  {
    name: "Circumstantial Analysis",
    value: AccountPlanTableType.CIRCUMSTANTIAL_ANALYSIS,
  },
  {
    name: "S.V.O.T Analysis",
    value: AccountPlanTableType.SVOT,
  },
  {
    name: "Insights and Perspectives",
    value: AccountPlanTableType.INSIGHT_AND_PERSPECTIVE,
  },
  {
    name: "Historic",
    value: AccountPlanTableType.HISTORIC_REVENUE,
  },
  {
    name: "Existing",
    value: AccountPlanTableType.CURRENT_REVENUE,
  },
  {
    name: "Current Opportunities In Play",
    value: AccountPlanTableType.CURRENT_OPPORTUNITY,
  },
  {
    name: "Potential Cross/Up-Sell Opportunities",
    value: AccountPlanTableType.POTENTIAL_OPPORTUNITY,
  },
  {
    name: "Forecast",
    value: AccountPlanTableType.REVENUE_FORECAST,
  },
  {
    name: "Information Needed",
    value: AccountPlanTableType.MISSING_INFORMATION,
  },
  {
    name: "Strategic Perception Development",
    value: AccountPlanTableType.TARGETED_PERCEPTION_DEVELOPMENT,
  },
  {
    name: "Strategic Considerations",
    value: AccountPlanTableType.ACTION_PLAN,
  },
  {
    name: "Strategic Actions",
    value: AccountPlanTableType.TOP_ACTION,
  },
  {
    name: "Client Meetings Scheduled",
    value: AccountPlanTableType.CLIENT_MEETING_SCHEDULE,
  },
];

export const getAccountPlanTableName = (type: AccountPlanTableType) =>
  accountPlanTableTypeList.find((v) => v.value === type)?.name ?? "";
