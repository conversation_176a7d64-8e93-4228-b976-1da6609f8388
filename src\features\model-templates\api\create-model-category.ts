import { useMutation } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { ModelCategory } from "../types/index";

export type CreateModelCategoryData = Pick<ModelCategory, "name">;

export const createModelCategory = ({
  data,
}: {
  data?: CreateModelCategoryData;
}): ApiResponse<ModelCategory> => {
  return api.post(API_ROUTES.MODEL_TEMPLATES_CATEGORIES, data);
};

type UseCreateModelCategoryOptions = {
  mutationConfig?: MutationConfig<typeof createModelCategory>;
};

export const useCreateModelCategory = ({
  mutationConfig,
}: UseCreateModelCategoryOptions) => {
  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: createModelCategory,
  });
};
