import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, BaseParams, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { OrganizationData } from "../types";

type OrganizationListParams = BaseParams;

export const getOrganizationList = ({
  params,
}: {
  params?: OrganizationListParams;
}): ApiResponse<OrganizationData[]> => {
  return api.get(API_ROUTES.ORGANIZATIONS, { params });
};

export const getOrganizationListQueryOptions = (
  params?: OrganizationListParams
) => {
  return queryOptions({
    queryKey: [QUERY_KEYS.ORGANIZATION, params].filter((v) => !!v),
    queryFn: () => getOrganizationList({ params }),
  });
};

type UseOrganizationListOptions = {
  params?: OrganizationListParams;
  queryConfig?: QueryConfig<typeof getOrganizationList>;
};

export const useOrganizationList = ({
  queryConfig,
  params,
}: UseOrganizationListOptions) => {
  const organizationListQuery = useQuery({
    ...getOrganizationListQueryOptions(params),
    ...queryConfig,
  });

  return {
    ...organizationListQuery,
    organizationList: organizationListQuery.data?.data || [],
  };
};
