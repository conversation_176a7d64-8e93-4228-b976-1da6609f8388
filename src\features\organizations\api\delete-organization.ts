import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { OrganizationData } from "../types";

export const deleteOrganization = ({
  organizationId,
}: {
  organizationId: number;
}): ApiResponse<OrganizationData> => {
  return api.delete(API_ROUTES.ORGANIZATIONS_DETAIL(organizationId));
};

type UseDeleteOrganizationOptions = {
  mutationConfig?: MutationConfig<typeof deleteOrganization>;
};

export const useDeleteOrganization = ({
  mutationConfig,
}: UseDeleteOrganizationOptions) => {
  const { onSuccess, invalidate, ...restConfig } = mutationConfig || {};
  const queryClient = useQueryClient();

  return useMutation({
    onSuccess: (...args) => {
      onSuccess?.(...args);

      if (invalidate) {
        queryClient.invalidateQueries({
          queryKey: [QUERY_KEYS.ORGANIZATION],
        });
      }
    },
    ...restConfig,
    mutationFn: deleteOrganization,
  });
};
