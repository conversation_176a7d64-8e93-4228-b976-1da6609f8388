export enum OrganizationTier {
  PUBLIC = "public",
  FREE = "free",
  PREMIUM = "premium",
  SUPERUSER = "superuser",
}

export type OrganizationCrm = {
  id: number;
  crm_type: string;
  access_token: string;
  refresh_token: string;
  instance_url?: string | null;
};

export type OrganizationData = {
  id: number;
  name: string | null;
  tagline: string | null;
  message: string | null;
  logo_url: string | null;
  image_url: string | null;
  subdomain: string | null;
  primary_color: string | null;
  primary_light: string | null;
  primary_extra_light: string | null;
  secondary_color: string | null;
  ap_wallet_share_color: string | null;
  ap_current_revenue_color: string | null;
  ap_current_opportunity_color: string | null;
  ap_potential_opportunity_color: string | null;
  unique_id: string | null;
  tier: OrganizationTier;
  organization_crms: OrganizationCrm[];
};

export type OrganizationPayload = Partial<OrganizationData>;
