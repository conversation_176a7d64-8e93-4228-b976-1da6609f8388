"use client";

import React from "react";

import {
  StakeholderMappingTable,
  WalletShareTable,
  CircumstantialAnalysisTable,
  SvotTable,
  InsightsAndPerspectiveTable,
  HistoricRevenueTable,
  CurrentRevenueTable,
  CurrentOpportunitiesTable,
  PotentialOpportunitiesTable,
  RevenueForecastTable,
  InformationNeededTable,
  StrategicPerceptionTable,
  StrategicConsiderationTable,
  MeetingScheduleTable,
  StrategicActionTable,
  AccountPlanTableContainer,
} from "@/features/account-plan/components/tables";

const Title = ({ title, tooltip }: { title: string; tooltip: string }) => (
  <div className="relative z-50 mb-4 flex items-center justify-center gap-res-y-xs">
    <p className="text-center text-xl font-bold text-primary-500">{title}</p>
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger>
          <IconHelpCircleFilled className="size-res-y-xl text-primary-500" />
        </TooltipTrigger>
        <TooltipContent
          className="flex-wrap whitespace-normal bg-white px-res-x-sm py-res-y-sm text-base font-semibold"
          side="bottom"
          align="center"
        >
          {tooltip}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  </div>
);

import { AccountPlanHead } from "@/features/account-plan/components/account-plan-head";
import {
  Tooltip,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { IconHelpCircleFilled } from "@tabler/icons-react";
import { TooltipContent } from "@radix-ui/react-tooltip";

import { useParams } from "next/navigation";
import { useAccountPlan } from "@/features/account-plan/api/get-account-plan";
import { useAccountPlanGroups } from "@/features/account-plan/api/account-plan-group/get-account-plan-group";

const AccountPlanSectionPage = () => {
  const { id } = useParams<{ id: string }>();
  const accountId = parseInt(id);

  const { accountPlan } = useAccountPlan({ accountId });
  const { accountPlanGroups } = useAccountPlanGroups({
    accountGroupId: accountPlan?.account_plan_group?.id,
  });

  return (
    <div className="flex h-full w-full">
      <div className="absolute h-[90%] w-[82%]">
        <AccountPlanTableContainer>
          <AccountPlanHead
            accountPlan={accountPlan}
            accountPlanGroups={accountPlanGroups}
          />

          <div
            className="flex items-center justify-around p-4"
            style={{
              backgroundColor: "rgb(244 244 245 / var(--tw-bg-opacity, 1))",
            }}
          >
            <div className="flex w-[31%] items-center justify-center">
              <Title
                title="Position"
                tooltip="Develop your positional knowledge of the AAA as the basis for setting your relationship strategy."
              />
            </div>

            <div className="flex w-[31%] items-center justify-center">
              <Title
                title="Revenue"
                tooltip="List historic and existing revenues as well as opportunities in play and cross/up-sell opportunities. Forecast your revenue for the next 6-60 months."
              />
            </div>

            <div className="flex w-[31%] items-center justify-center">
              <Title
                title="Strategy"
                tooltip="Develop a strategic action plan that will advance perceptions of key stakeholders and increase revenue through the development of new cross/up-sell opportunities."
              />
            </div>
          </div>

          <div className="flex items-center justify-around rounded-xl bg-white p-4 shadow-md">
            <div className="w-[31%]">
              <div className="flex h-full flex-col gap-3">
                <StakeholderMappingTable />

                <WalletShareTable accountPlan={accountPlan} />

                <CircumstantialAnalysisTable />

                <SvotTable />

                <InsightsAndPerspectiveTable />
              </div>
            </div>

            <div className="w-[31%]">
              <div className="flex h-full flex-col gap-3">
                <HistoricRevenueTable accountPlan={accountPlan} />

                <CurrentRevenueTable accountPlan={accountPlan} />

                <CurrentOpportunitiesTable accountPlan={accountPlan} />

                <PotentialOpportunitiesTable accountPlan={accountPlan} />

                <RevenueForecastTable accountPlan={accountPlan} />
              </div>
            </div>

            <div className="w-[31%]">
              <div className="flex h-full flex-col gap-3">
                <InformationNeededTable />

                <StrategicPerceptionTable />

                <StrategicConsiderationTable />

                <StrategicActionTable />

                <MeetingScheduleTable />
              </div>
            </div>
          </div>
        </AccountPlanTableContainer>
      </div>
    </div>
  );
};

export default AccountPlanSectionPage;
