import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import {
  APHistoricRevenue,
  APHistoricRevenueBaseData,
} from "@/features/account-plan/types/revenue-types";

export const updateHistoricRevenue = ({
  accountId,
  id,
  data,
}: {
  id: number;
  accountId: number;
  data?: APHistoricRevenueBaseData;
}): ApiResponse<APHistoricRevenue> => {
  return api.put(
    API_ROUTES.ACCOUNT_PLANS_HISTORIC_REVENUE_DETAIL(accountId, id),
    data
  );
};

type UseUpdateHistoricRevenueOptions = {
  mutationConfig?: MutationConfig<typeof updateHistoricRevenue>;
};

export const useUpdateHistoricRevenue = ({
  mutationConfig,
}: UseUpdateHistoricRevenueOptions) => {
  const queryClient = useQueryClient();
  const { onSuccess, invalidate, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      if (invalidate) {
        await queryClient.invalidateQueries({
          queryKey: [
            QUERY_KEYS.ACCOUNT_PLANS,
            args[1].accountId,
            QUERY_KEYS.ACCOUNT_PLANS_HISTORIC_REVENUE,
          ],
        });
      }

      onSuccess?.(...args);
    },

    ...restConfig,
    mutationFn: updateHistoricRevenue,
  });
};
