"use client";

import withNotLoginRoute from "@/features/auth/components/not-login-route";
import {
  UserAgreementItem,
  UserAgreementList,
  UserAgreementsLayout,
} from "@/features/landing-page/user-agreements-layout";

function PrivacyPolicy() {
  return (
    <UserAgreementsLayout>
      <h1 className="text-3xl font-semibold">Privacy Policy</h1>
      <p className="mt-4">
        <strong>Perception Selling.ai</strong> (“we”, “our”, “us”) is committed
        to protecting your personal data in accordance with the
        <strong> Personal Data Protection Act 2012 (PDPA)</strong> of Singapore.
        This Privacy Policy outlines how we collect, use, disclose, and
        safeguard personal data in the course of providing our services.
      </p>

      <div className="mt-8 space-y-6">
        <UserAgreementItem title="1. Scope">
          <p>
            This policy applies to all users of our platform, including
            organizations and their authorized users, and covers both personal
            and business information you submit to us.
          </p>
        </UserAgreementItem>

        <UserAgreementItem title="2. Types of Data Collected">
          <p>
            In the course of using our service, we may collect the following:
          </p>

          <h3 className="mt-4 font-semibold">
            Personal Data{" "}
            <span className="font-normal">(as defined under PDPA)</span>:
          </h3>
          <UserAgreementList>
            <li>Names and email addresses of employees</li>
            <li>Names and contact information of clients or stakeholders</li>
          </UserAgreementList>

          <h3 className="mt-4 font-semibold">Business & Strategic Data:</h3>
          <UserAgreementList>
            <li>
              Client and stakeholder details, roles, and organizational
              information
            </li>
            <li>SVWOT inputs and strategic planning data</li>
            <li>Historical revenue (not processed through LLM)</li>
            <li>Strategic actions and client meeting information</li>
          </UserAgreementList>
        </UserAgreementItem>

        <UserAgreementItem title="3. Purpose of Collection">
          <p>
            We collect and use your data strictly for the following purposes:
          </p>
          <UserAgreementList>
            <li>To deliver platform functionality and insights</li>
            <li>To enhance the user experience and product features</li>
            <li>For account setup, support, and customer success activities</li>
            <li>For internal audits, analytics, and compliance</li>
          </UserAgreementList>
          <p>
            Where required, we will seek your consent before collecting or using
            your personal data for purposes beyond the original intent.
          </p>
        </UserAgreementItem>

        <UserAgreementItem title="4. Disclosure to Third Parties">
          <p>
            We <strong>do not sell</strong>, rent, or trade your data to any
            third parties. We may engage trusted service providers (e.g.,
            hosting, LLM infrastructure providers) who are contractually bound
            to protect the confidentiality of your data and act only on our
            instructions.
          </p>
          <p>
            No financial or sensitive business data is disclosed to any external
            service without your express consent.
          </p>
        </UserAgreementItem>

        <UserAgreementItem title="5. Use of LLMs (Large Language Models)">
          <p>
            Certain non-financial strategic inputs may be processed through
            secure LLM APIs for the purpose of generating insights. This
            includes:
          </p>
          <UserAgreementList>
            <li>Stakeholder mapping</li>
            <li>Account planning recommendations</li>
            <li>Opportunity analysis</li>
          </UserAgreementList>

          <p className="mt-4">
            We do <strong>not</strong> pass the following data into LLMs:
          </p>
          <UserAgreementList>
            <li>Revenue figures</li>
            <li>Employee email addresses</li>
            <li>Meeting schedules</li>
          </UserAgreementList>

          <p>Where applicable, data is anonymized or pseudonymized.</p>
        </UserAgreementItem>

        <UserAgreementItem title="6. Data Protection and Retention">
          <UserAgreementList>
            <li>
              Data is stored securely using encryption at rest and in transit
            </li>
            <li>Access to data is role-based and audited regularly</li>
            <li>
              Data is retained only for as long as necessary to fulfill business
              or legal purposes
            </li>
          </UserAgreementList>
          <p>
            You may request access, correction, or deletion of your personal
            data at any time by contacting us.
          </p>
        </UserAgreementItem>

        <UserAgreementItem title="7. Your Rights Under PDP">
          <p>You have the right to:</p>
          <UserAgreementList>
            <li>Access personal data we hold about you</li>
            <li>Correct inaccurate data</li>
            <li>Withdraw consent (where applicable)</li>
            <li>
              Request data erasure, subject to legal or regulatory exceptions
            </li>
          </UserAgreementList>
        </UserAgreementItem>

        <UserAgreementItem title="8. Contact Us">
          <p>
            For data access requests, questions, or feedback about our data
            protection practices, please contact:{" "}
            <a
              href="mailto:<EMAIL>"
              className="text-blue-600 underline"
            >
              <EMAIL>
            </a>
          </p>
        </UserAgreementItem>
      </div>
    </UserAgreementsLayout>
  );
}

export default withNotLoginRoute(PrivacyPolicy);
