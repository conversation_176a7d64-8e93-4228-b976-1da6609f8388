import _ from "lodash";

import {
  PdfMarkdown,
  PdfTable,
  PdfTableCell,
  PdfTableHeader,
  PdfTableRow,
  PdfTableTitle,
} from "../";
import { AccountPlanAnalysisPDFProps } from "../../download-analysis";
import { Fragment } from "react";
import { getAccountPlanTableName } from "@/features/account-plan/constants";
import { AccountPlanTableType } from "@/features/account-plan/types";

export const CircumstantialAnalysisPDFTable = ({
  data,
}: {
  data?: AccountPlanAnalysisPDFProps["circumstantialAnalysisList"];
}) => {
  return (
    <PdfTable>
      <PdfTableTitle>
        {" "}
        {getAccountPlanTableName(AccountPlanTableType.CIRCUMSTANTIAL_ANALYSIS)}
      </PdfTableTitle>

      <PdfTableRow>
        <PdfTableHeader>News and Events</PdfTableHeader>
      </PdfTableRow>

      {data
        ?.filter((v) => !!v.item_type)
        .map((row, idx) => (
          <Fragment key={idx}>
            <PdfTableRow>
              <PdfTableCell
                style={{
                  backgroundColor: "#e5e5e5",
                  textAlign: "left",
                  fontWeight: 700,
                }}
              >
                {_.capitalize(row.item_type)}
              </PdfTableCell>
            </PdfTableRow>
            <PdfTableRow>
              <PdfMarkdown style={{ flex: 3 }}>{row.description}</PdfMarkdown>
            </PdfTableRow>
          </Fragment>
        ))}
    </PdfTable>
  );
};
