export enum LLMConsiderationUsage {
  AP_CHARTS = "ap_charts",
  AP_SET = "ap_set",
  APTABLE_CIRCUMSTANTIAL_ANALYSIS = "aptable_circumstantial_analyis_generate",
}

export type LLMConsiderationData = {
  id: number;
  organization_id: number;
  usage_on: LLMConsiderationUsage;
  result: Array<{
    chat_id: number;
    message: string;
    message_id: number;
    account_plan_name: string;
    account_plan_group_id: number;
  }>;
  updated_at: string;
};
