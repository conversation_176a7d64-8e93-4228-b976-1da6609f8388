import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";

export const generateCircumstantialAnalysis = ({
  accountId,
}: {
  accountId: number;
}): ApiResponse => {
  return api.post(
    API_ROUTES.ACCOUNT_PLANS_CIRCUMSTANTIAL_ANALYSIS_GENERATE(accountId)
  );
};

type UseGenerateCircumstantialAnalysisOptions = {
  mutationConfig?: MutationConfig<typeof generateCircumstantialAnalysis>;
};

export const useGenerateCircumstantialAnalysis = ({
  mutationConfig,
}: UseGenerateCircumstantialAnalysisOptions) => {
  const queryClient = useQueryClient();
  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      await queryClient.invalidateQueries({
        queryKey: [
          QUERY_KEYS.ACCOUNT_PLANS,
          args[1].accountId,
          QUERY_KEYS.ACCOUNT_PLANS_CIRCUMSTANTIAL_ANALYSIS,
        ],
      });

      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: generateCircumstantialAnalysis,
  });
};
