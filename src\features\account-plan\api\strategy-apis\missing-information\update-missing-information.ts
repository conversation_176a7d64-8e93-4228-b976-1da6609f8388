import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import {
  APMissingInformation,
  APMissingInformationBaseData,
} from "@/features/account-plan/types/strategy-types";

export const updateMissingInformation = ({
  accountId,
  id,
  data,
}: {
  id: number;
  accountId: number;
  data?: APMissingInformationBaseData;
}): ApiResponse<APMissingInformation> => {
  return api.put(
    API_ROUTES.ACCOUNT_PLANS_MISSING_INFORMATION_DETAIL(accountId, id),
    data
  );
};

type UseUpdateMissingInformationOptions = {
  mutationConfig?: MutationConfig<typeof updateMissingInformation>;
};

export const useUpdateMissingInformation = ({
  mutationConfig,
}: UseUpdateMissingInformationOptions) => {
  const queryClient = useQueryClient();
  const { onSuccess, invalidate, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      if (invalidate) {
        await queryClient.invalidateQueries({
          queryKey: [
            QUERY_KEYS.ACCOUNT_PLANS,
            args[1].accountId,
            QUERY_KEYS.ACCOUNT_PLANS_MISSING_INFORMATION,
          ],
        });
      }

      onSuccess?.(...args);
    },

    ...restConfig,
    mutationFn: updateMissingInformation,
  });
};
