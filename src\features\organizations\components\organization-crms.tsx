"use client";

import { OrganizationData } from "../types";
import { OrganizationCrmForm } from "./organization-crm.form";

type Props = {
  organization: OrganizationData;
};

export const OrganzationCrms = ({ organization }: Props) => {
  return (
    <>
      <div className="flex flex-col gap-4 rounded-lg bg-white p-6 text-primary-500 shadow-md">
        <div className="my-2 flex items-center justify-between">
          <h2 className="text-lg font-semibold">Connected CRMs</h2>

          <OrganizationCrmForm organizationId={organization?.id} />
        </div>

        {organization?.organization_crms?.map((crm) => (
          <div
            key={crm?.id}
            className="flex w-full items-center justify-between rounded-lg bg-gray-200 px-4 py-8"
          >
            <div className="flex flex-col gap-2">
              <div className="text-xl font-semibold">
                {crm?.crm_type?.charAt(0).toUpperCase() +
                  crm?.crm_type?.slice(1)}
              </div>
              <span className="text-md text-gray-600">
                {crm?.instance_url || "No instance URL"}
              </span>
            </div>

            <OrganizationCrmForm
              isEdit
              crm={crm}
              organizationId={organization?.id}
            />
          </div>
        ))}
      </div>
      {organization?.organization_crms?.length === 0 && (
        <p className="text-gray-500">No CRMs connected.</p>
      )}
    </>
  );
};
