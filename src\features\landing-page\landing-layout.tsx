"use client";

import { IconExclamationCircle, IconX } from "@tabler/icons-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { cn } from "@/lib/utils";
import { useLandingPageStore } from "./stores";

function LandingLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const { showDeviceAlert, toggleDeviceAlert } = useLandingPageStore();

  return (
    <div className="flex h-screen w-screen items-center justify-center bg-background font-poppins text-white">
      <Alert
        className={cn(
          "transition-default fixed top-0 block rounded-ss-none xl:hidden",
          showDeviceAlert ? "translate-y-0" : "-translate-y-full"
        )}
      >
        <IconExclamationCircle className="size-4" />
        <AlertTitle>Mobile Device Detected</AlertTitle>
        <AlertDescription>
          This app isn’t optimized for mobile. For the best experience, please
          use a desktop device.
        </AlertDescription>
        <button
          className="absolute right-3 top-3 text-muted-foreground transition-colors hover:text-foreground"
          onClick={toggleDeviceAlert}
        >
          <IconX className="h-4 w-4" />
        </button>
      </Alert>

      {children}
    </div>
  );
}

export default LandingLayout;
