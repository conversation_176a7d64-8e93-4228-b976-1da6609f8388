import * as React from "react";

import { Input } from ".";
import { IconEye, IconEyeOff } from "@tabler/icons-react";

export interface PasswordInputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {}

const PasswordInput = React.forwardRef<HTMLInputElement, PasswordInputProps>(
  ({ className, ...props }, ref) => {
    const [showPassword, setShowPassword] = React.useState(false);

    return (
      <Input
        type={showPassword ? "text" : "password"}
        endIcon={showPassword ? IconEyeOff : IconEye}
        className={className}
        ref={ref}
        iconProps={{
          className: "cursor-pointer",
          onClick: () => setShowPassword(!showPassword),
        }}
        {...props}
      />
    );
  }
);
PasswordInput.displayName = "PasswordInput";

export { PasswordInput };
