import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { AccountPlanPayload, AccountPlanData } from "../types";
import { QUERY_KEYS } from "@/constants/query-keys";

export const createAccountPlan = ({
  data,
}: {
  data?: AccountPlanPayload;
}): ApiResponse<AccountPlanData> => {
  return api.post(`${API_ROUTES.ACCOUNT_PLANS}`, data);
};

type UseCreateAccountPlanOptions = {
  mutationConfig?: MutationConfig<typeof createAccountPlan>;
};

export const useCreateAccountPlan = ({
  mutationConfig,
}: UseCreateAccountPlanOptions) => {
  const { onSuccess, invalidate, ...restConfig } = mutationConfig || {};
  const queryClient = useQueryClient();

  return useMutation({
    onSuccess: (...args) => {
      onSuccess?.(...args);

      if (invalidate) {
        queryClient.invalidateQueries({
          queryKey: [QUERY_KEYS.ACCOUNT_PLANS],
        });

        queryClient.invalidateQueries({
          queryKey: [
            QUERY_KEYS.ACCOUNT_PLANS_GROUPS,
            args[1].data?.account_plan_group_id,
          ],
        });
      }
    },
    ...restConfig,
    mutationFn: createAccountPlan,
  });
};
