"use client";

import { cn } from "@/lib/utils";
import * as React from "react";

interface SpinnerProps extends React.HTMLAttributes<HTMLDivElement> {
  className?: string;
}

const Spinner = React.forwardRef<HTMLDivElement, SpinnerProps>((props, ref) => {
  const { className, ...rest } = props;
  return (
    <div
      ref={ref}
      className={cn(
        "h-6 w-6 animate-spin rounded-full border-2 border-t-2 border-gray-200 border-t-gray-600",
        className
      )}
      {...rest}
    />
  );
});

Spinner.displayName = "Spinner";

export { Spinner };
