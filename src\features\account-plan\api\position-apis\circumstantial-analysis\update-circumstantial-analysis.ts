import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import {
  APCircumstantialAnalysis,
  APCircumstantialAnalysisBaseData,
} from "@/features/account-plan/types/position-types";

export const updateCircumstantialAnalysis = ({
  accountId,
  id,
  data,
}: {
  id: number;
  accountId: number;
  data?: APCircumstantialAnalysisBaseData;
}): ApiResponse<APCircumstantialAnalysis> => {
  return api.put(
    API_ROUTES.ACCOUNT_PLANS_CIRCUMSTANTIAL_ANALYSIS_DETAIL(accountId, id),
    data
  );
};

type UseUpdateCircumstantialAnalysisOptions = {
  mutationConfig?: MutationConfig<typeof updateCircumstantialAnalysis>;
};

export const useUpdateCircumstantialAnalysis = ({
  mutationConfig,
}: UseUpdateCircumstantialAnalysisOptions) => {
  const queryClient = useQueryClient();
  const { onSuccess, invalidate, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      if (invalidate) {
        await queryClient.invalidateQueries({
          queryKey: [
            QUERY_KEYS.ACCOUNT_PLANS,
            args[1].accountId,
            QUERY_KEYS.ACCOUNT_PLANS_CIRCUMSTANTIAL_ANALYSIS,
          ],
        });
      }

      onSuccess?.(...args);
    },

    ...restConfig,
    mutationFn: updateCircumstantialAnalysis,
  });
};
