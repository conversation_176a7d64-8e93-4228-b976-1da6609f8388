import { getAccountPlanTableName } from "@/features/account-plan/constants";
import {
  PdfTable,
  PdfTableCell,
  PdfTableHeader,
  PdfTableRow,
  PdfTableTitle,
} from "../";
import { AccountPlanAnalysisPDFProps } from "../../download-analysis";
import { AccountPlanTableType } from "@/features/account-plan/types";

export const StakeholderPDFTable = ({
  data,
}: {
  data?: AccountPlanAnalysisPDFProps["stakeholderMappingList"];
}) => {
  const headers = [
    "Name",
    "Job Title",
    "Location",
    "Influence",
    "Role",
    "Perception",
    "Advocacy",
    "Coverage",
  ];

  return (
    <PdfTable>
      <PdfTableTitle>
        {getAccountPlanTableName(AccountPlanTableType.STAKEHOLDER_MAPPING)}
      </PdfTableTitle>

      <PdfTableRow>
        {headers.map((header, idx) => (
          <PdfTableHeader key={idx}>{header}</PdfTableHeader>
        ))}
      </PdfTableRow>

      {data?.map((row, idx) => (
        <PdfTableRow key={idx}>
          <PdfTableCell>{row.name}</PdfTableCell>
          <PdfTableCell>{row.job_title}</PdfTableCell>
          <PdfTableCell>{row.location}</PdfTableCell>
          <PdfTableCell>{row.influence}</PdfTableCell>
          <PdfTableCell>{row.role?.join(", ")}</PdfTableCell>
          <PdfTableCell>{row.perception}</PdfTableCell>
          <PdfTableCell>{row.advocacy}</PdfTableCell>
          <PdfTableCell>{row.coverage ? "Yes" : "No"}</PdfTableCell>
        </PdfTableRow>
      ))}
    </PdfTable>
  );
};
