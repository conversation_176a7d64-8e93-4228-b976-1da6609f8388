import { type ColumnDef } from "@tanstack/react-table";
import { useParams } from "next/navigation";
import { useEffect, useMemo, useState } from "react";
import _ from "lodash";

import { AccountPlanTableType } from "@/features/account-plan/types";
import {
  APCircumstantialAnalysis,
  APCircumstantialAnalysisType,
} from "@/features/account-plan/types/position-types";
import DataTable, { DataTableMeta } from "@/components/ui/data-table";
import { useCircumstantialAnalysisList } from "@/features/account-plan/api/position-apis/circumstantial-analysis/get-circumstantial-analysis-list";
import { TiptapCell } from "@/components/ui/data-table/data-table-components";
import { useGenerateCircumstantialAnalysis } from "@/features/account-plan/api/position-apis/circumstantial-analysis/create-circumstantial-analysis-generate";
import { AccountTable, AccountTableTitle } from "../base-table";
import { toast } from "sonner";
import { getAccountPlanTableName } from "@/features/account-plan/constants";

const circumstantialAnalysisOptions = {
  [APCircumstantialAnalysisType.MACRO]: {
    order: 1,
    name: "Macro",
  },
  [APCircumstantialAnalysisType.INDUSTRY]: {
    order: 2,
    name: "Industry",
  },
  [APCircumstantialAnalysisType.BUSINESS]: {
    order: 3,
    name: "Business",
  },
} as const;

export const CircumstantialAnalysisTable = () => {
  const [tableData, setTableData] = useState<APCircumstantialAnalysis[]>([]);

  const { id } = useParams<{ id: string }>();
  const accountId = parseInt(id);

  const { circumstantialAnalysisList } = useCircumstantialAnalysisList({
    accountId,
    params: {
      disable_pagination: true,
    },
  });
  const generateCircumstantialAnalysis = useGenerateCircumstantialAnalysis({});

  useEffect(() => {
    if (!circumstantialAnalysisList) return;

    const newTableData = circumstantialAnalysisList
      ?.map((v, idx) => ({
        idx,
        ...v,
      }))
      .sort(
        (a, b) =>
          circumstantialAnalysisOptions[a.item_type].order -
          circumstantialAnalysisOptions[b.item_type].order
      );

    setTableData(newTableData);
  }, [circumstantialAnalysisList]);

  const columns: ColumnDef<APCircumstantialAnalysis>[] = useMemo(
    () => [
      {
        accessorKey: "description",
        header: "News, Trends and Events",
        cell: ({ row, table }) => {
          const isPreview =
            (table.options.meta as DataTableMeta)?.isPreview ?? false;
          const rowData = row.original;
          const itemType = circumstantialAnalysisOptions[rowData.item_type];

          return (
            <div className={isPreview ? "text-4xl" : "text-lg"}>
              <div className="bg-neutral-200 px-res-x-base py-res-y-base font-bold">
                {itemType.name}
              </div>
              <TiptapCell
                className={`min-h-[7.5vh] px-res-x-base py-res-y-base ${isPreview ? "text-4xl" : "text-lg"}`}
                value={rowData.description ?? ""}
                editable={false}
                onChange={() => {}}
              />
            </div>
          );
        },
        meta: {
          tooltip:
            "Provides detailed context or explanation regarding the analysis type, focusing on its purpose, scope, and key insights. Helps users understand the relevance and findings of the analysis.",
        },
      },
    ],
    []
  );

  const onGenerateCircumstantialAnalysis = async () => {
    try {
      await generateCircumstantialAnalysis.mutateAsync({
        accountId,
      });

      toast("Successfully generated the circumstantial nalysis");
    } catch (_) {
      toast("An error occured while generating the circumstantial analysis");
    }
  };

  return (
    <AccountTable
      type={AccountPlanTableType.CIRCUMSTANTIAL_ANALYSIS}
      onGenerate={onGenerateCircumstantialAnalysis}
      isLoading={generateCircumstantialAnalysis.isPending}
    >
      <DataTable
        columns={columns}
        data={tableData}
        headerClassName="!text-center"
        tableType={AccountPlanTableType.CIRCUMSTANTIAL_ANALYSIS}
      />
    </AccountTable>
  );
};
