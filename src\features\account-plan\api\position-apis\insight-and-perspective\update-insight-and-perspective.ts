import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import {
  APInsightAndPerspective,
  APInsightAndPerspectiveBaseData,
} from "@/features/account-plan/types/position-types";

export const updateInsightAndPerspective = ({
  accountId,
  id,
  data,
}: {
  id: number;
  accountId: number;
  data?: APInsightAndPerspectiveBaseData;
}): ApiResponse<APInsightAndPerspective> => {
  return api.put(
    API_ROUTES.ACCOUNT_PLANS_INSIGHT_AND_PERSPECTIVE_DETAIL(accountId, id),
    data
  );
};

type UseUpdateInsightAndPerspectiveOptions = {
  mutationConfig?: MutationConfig<typeof updateInsightAndPerspective>;
};

export const useUpdateInsightAndPerspective = ({
  mutationConfig,
}: UseUpdateInsightAndPerspectiveOptions) => {
  const queryClient = useQueryClient();
  const { onSuccess, invalidate, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      if (invalidate) {
        await queryClient.invalidateQueries({
          queryKey: [
            QUERY_KEYS.ACCOUNT_PLANS,
            args[1].accountId,
            QUERY_KEYS.ACCOUNT_PLANS_INSIGHT_AND_PERSPECTIVE,
          ],
        });
      }

      onSuccess?.(...args);
    },

    ...restConfig,
    mutationFn: updateInsightAndPerspective,
  });
};
