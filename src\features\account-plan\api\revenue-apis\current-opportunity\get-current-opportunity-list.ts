import { queryOptions, useQueries, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, BaseParams, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { APCurrentOpportunity } from "@/features/account-plan/types/revenue-types";

type CurrentOpportunityListParams = BaseParams;

export const getCurrentOpportunityList = ({
  accountId,
  params,
}: {
  accountId: number;
  params?: CurrentOpportunityListParams;
}): ApiResponse<APCurrentOpportunity[]> => {
  return api.get(API_ROUTES.ACCOUNT_PLANS_CURRENT_OPPORTUNITY(accountId), {
    params,
  });
};

export const getCurrentOpportunityListQueryOptions = (
  accountId: number,
  params?: CurrentOpportunityListParams
) => {
  return queryOptions({
    queryKey: [
      QUERY_KEYS.ACCOUNT_PLANS,
      accountId,
      QUERY_KEYS.ACCOUNT_PLANS_CURRENT_OPPORTUNITY,
    ],
    queryFn: () => getCurrentOpportunityList({ accountId, params }),
    enabled: !!accountId,
  });
};

type UseCurrentOpportunityListOptions = {
  params?: CurrentOpportunityListParams;
  accountId: number;
  queryConfig?: QueryConfig<typeof getCurrentOpportunityList>;
  options?: Partial<ReturnType<typeof getCurrentOpportunityListQueryOptions>>;
};

export const useCurrentOpportunityList = ({
  params,
  accountId,
  queryConfig,
  options,
}: UseCurrentOpportunityListOptions) => {
  const currentOpportunityListQuery = useQuery({
    ...getCurrentOpportunityListQueryOptions(accountId, params),
    ...queryConfig,
    ...options,
  });

  return {
    ...currentOpportunityListQuery,
    currentOpportunityList: currentOpportunityListQuery.data?.data,
  };
};

type UseCurrentOpportunityListsOptions = {
  queries: {
    accountId: number;
    params?: CurrentOpportunityListParams;
  }[];
  queryConfig?: QueryConfig<typeof getCurrentOpportunityList>;
  options?: Partial<ReturnType<typeof getCurrentOpportunityListQueryOptions>>;
};

export const useCurrentOpportunityLists = ({
  queries,
  queryConfig,
}: UseCurrentOpportunityListsOptions) => {
  const results = useQueries({
    queries: queries.map(({ accountId, params }) => ({
      ...getCurrentOpportunityListQueryOptions(accountId, params),
      ...queryConfig,
    })),
  });

  return results.map((result, idx) => ({
    ...result,
    query: queries[idx],
    currentOpportunityList: result.data?.data ?? [],
  }));
};
