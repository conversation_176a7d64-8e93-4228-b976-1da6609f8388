import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { OrganizationData } from "../types";

export const getOrganization = ({
  organizationId,
}: {
  organizationId?: number;
}): ApiResponse<OrganizationData> => {
  return api.get(API_ROUTES.ORGANIZATIONS_DETAIL(organizationId));
};

export const getOrganizationQueryOptions = (organizationId?: number) => {
  return queryOptions({
    queryKey: [QUERY_KEYS.ORGANIZATION, organizationId],
    queryFn: () => getOrganization({ organizationId }),
    enabled: typeof organizationId === "number",
  });
};

type UseOrganizationOptions = {
  organizationId?: number;
  queryConfig?: QueryConfig<typeof getOrganization>;
  options?: Partial<ReturnType<typeof getOrganizationQueryOptions>>;
};

export const useOrganization = ({
  organizationId,
  queryConfig,
  options,
}: UseOrganizationOptions) => {
  const organizationQuery = useQuery({
    ...getOrganizationQueryOptions(organizationId),
    ...queryConfig,
    ...options,
  });

  return {
    ...organizationQuery,
    organization: organizationQuery.data?.data,
    primaryColor:
      organizationQuery.data?.data?.primary_color ?? "var(--primary-500)",
    secondaryColor:
      organizationQuery.data?.data?.secondary_color ?? "var(--primary-500)",
  };
};
