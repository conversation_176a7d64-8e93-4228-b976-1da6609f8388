# Backend API URL - REQUIRED for the app to function
# Update this to point to your actual backend API server
NEXT_PUBLIC_API_URL=http://localhost:8000/api

# Optional: Sentry error tracking
# Add your Sentry auth token if you want error tracking
SENTRY_AUTH_TOKEN=

# Development environment flag
NODE_ENV=development

# Optional: Database URL if needed for any server-side operations
# DATABASE_URL=

# Optional: JWT secret for any server-side token operations
# JWT_SECRET=

# Optional: S3 configuration if file uploads are used
# AWS_ACCESS_KEY_ID=
# AWS_SECRET_ACCESS_KEY=
# AWS_REGION=
# AWS_S3_BUCKET=

# Optional: Email service configuration
# EMAIL_SERVICE_API_KEY=
# EMAIL_FROM=

# Optional: Additional API keys for third-party services
# OPENAI_API_KEY=
# ANTHROPIC_API_KEY=
