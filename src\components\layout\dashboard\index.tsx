"use client";

import React, { useMemo } from "react";
import { PATH } from "@/constants/path";
import { useLogout } from "@/features/auth/api/logout";
import Link from "next/link";
import { useRolesPermissions } from "@/features/user-management/hooks/use-role-permissions";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { usePathname } from "next/navigation";
import { Icon, icons } from "@/components/ui/icon";

type SidebarMenuItem = {
  name: string;
  icon: keyof typeof icons;
  show?: boolean;
  tooltip: string;
  path: string;
  onClick?: () => void;
};

const SidebarMenu = () => {
  const logout = useLogout();
  const permissions = useRolesPermissions();
  const pathName = usePathname();

  const sidebarMenu: SidebarMenuItem[] = useMemo(
    () =>
      [
        {
          name: "Dashboard",
          icon: "dashboard" as keyof typeof icons,
          path: PATH.DASHBOARD,
          tooltip: "View and manage all accounts in your portfolio",
        },
        {
          name: "Account Plans",
          icon: "accountPlan" as keyof typeof icons,
          path: PATH.DASHBOARD_ACCOUNT_PLAN,
          tooltip: "Access and edit detailed plans for individual accounts",
        },
        {
          name: "User Management",
          icon: "userManagement" as keyof typeof icons,
          path: PATH.DASHBOARD_USER_MANAGEMENT,
          show: permissions.isAbleToInvite,
          tooltip: "Manage user roles, permissions, and account access",
        },
        {
          name: "Tuning Engine",
          icon: "tuningEngine" as keyof typeof icons,
          path: PATH.DASHBOARD_TUNING_ENGINE,
          show: permissions.isAbleToManageTemplate,
          tooltip:
            "Adjust and optimize AI settings for better performance and insights",
        },
        {
          name: "Add Template",
          icon: "addTemplate" as keyof typeof icons,
          path: PATH.DASHBOARD_TUNING_ENGINE_CREATE,
          show: permissions.isAbleToManageTemplate,
          tooltip: "Create and manage custom templates for AI tuning",
        },
        {
          name: "Settings",
          icon: "settings" as keyof typeof icons,
          path: PATH.DASHBOARD_SETTINGS,
          show: permissions.isAbleToManageSettings,
          tooltip: "Configure application preferences and system settings",
        },
        {
          name: "Logout",
          icon: "logout" as keyof typeof icons,
          show: true,
          path: PATH.LANDING,
          onClick: () => logout(),
          tooltip: "Sign out of the account and end the current session",
        },
      ].filter((v) => v?.show === undefined || v?.show),
    [logout, permissions]
  );

  return (
    <>
      {sidebarMenu.map((v, idx) => {
        const isActive = pathName == v.path;

        return (
          <TooltipProvider key={idx}>
            <Tooltip>
              <TooltipTrigger>
                <Link
                  className={`group flex w-52 cursor-pointer items-center justify-start gap-2 rounded-lg p-4 ${isActive ? "bg-gradient text-white" : "text-font-primary hover:bg-primary-100"}`}
                  href={v.path}
                  onClick={v.onClick}
                >
                  <Icon
                    name={v.icon}
                    className={`h-8 w-8 ${isActive ? "text-white" : "text-font-primary"}`}
                  />
                  <p className="text-center text-sm font-semibold">{v.name}</p>
                </Link>
              </TooltipTrigger>
              <TooltipContent side="right">{v.tooltip}</TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
      })}
    </>
  );
};

export { SidebarMenu };
