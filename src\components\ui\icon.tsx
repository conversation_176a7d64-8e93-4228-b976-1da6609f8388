import { ComponentProps, ReactElement } from "react";
import Dashboard from "@/assets/dashboard.svg";
import AccountPlan from "@/assets/account-plans.svg";
import TuningEngine from "@/assets/tuning-engine.svg";
import AddTemplate from "@/assets/add-template.svg";
import { IconUsersGroup } from "@tabler/icons-react";
import Settings from "@/assets/settings.svg";
import LogOut from "@/assets/log-out.svg";

export const icons = {
  dashboard: Dashboard,
  accountPlan: AccountPlan,
  tuningEngine: TuningEngine,
  addTemplate: AddTemplate,
  userManagement: IconUsersGroup,
  settings: Settings,
  logout: LogOut,
};

type Props = {
  name: keyof typeof icons;
} & ComponentProps<"svg">;

export const Icon = ({ name, ...props }: Props) => {
  const Component = icons[name];

  return (
    <Component className="icon" width={20} height={20} {...props} />
  ) as ReactElement;
};
