import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, BaseParams, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { AccountPlanGroupsData, AccountPlanGroupStatus } from "../../types";

type AccountListParams = Omit<BaseParams, "sort_column"> & {
  status?: AccountPlanGroupStatus;
  account_plan_unique_id?: string;
  ap_owner?: string;
  company?: string;
  sort_column?:
    | "review_date"
    | "next_review"
    | "industry"
    | "currency"
    | "company"
    | "account_plan_unique_id";
};

export const getAccountPlanGroupsList = ({
  params,
}: {
  params?: AccountListParams;
}): ApiResponse<AccountPlanGroupsData[]> => {
  return api.get(API_ROUTES.ACCOUNT_PLAN_GROUPS, { params });
};

export const getAccountPlanGroupsListQueryOptions = (
  params?: AccountListParams
) => {
  return queryOptions({
    queryKey: [QUERY_KEYS.ACCOUNT_PLANS_GROUPS, params],
    queryFn: () => getAccountPlanGroupsList({ params }),
  });
};

type UseAccountPlanGroupsOptions = {
  params?: AccountListParams;
  queryConfig?: QueryConfig<typeof getAccountPlanGroupsList>;
};

export const useAccountPlanGroupsList = ({
  queryConfig,
  params,
}: UseAccountPlanGroupsOptions) => {
  const accountPlanGroupsListQuery = useQuery({
    ...getAccountPlanGroupsListQueryOptions(params),
    ...queryConfig,
  });

  return {
    ...accountPlanGroupsListQuery,
    accountPlanGroupsList: accountPlanGroupsListQuery.data?.data || [],
  };
};
