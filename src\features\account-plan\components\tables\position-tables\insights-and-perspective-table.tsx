import { type ColumnDef } from "@tanstack/react-table";
import { useParams } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";
import _ from "lodash";

import { AccountPlanTableType } from "@/features/account-plan/types";
import { AccountTable, AccountTableTitle } from "../base-table";
import { APInsightAndPerspective } from "@/features/account-plan/types/position-types";
import DataTable, { DataTableMeta } from "@/components/ui/data-table";
import { SelectCell } from "@/components/ui/data-table/data-table-components";

import { toast } from "sonner";

import { useInsightAndPerspectiveList } from "@/features/account-plan/api/position-apis/insight-and-perspective/get-insight-and-perspective-list";
import { useUpdateInsightAndPerspective } from "@/features/account-plan/api/position-apis/insight-and-perspective/update-insight-and-perspective";
import { useDeleteInsightAndPerspective } from "@/features/account-plan/api/position-apis/insight-and-perspective/delete-insight-and-perspective";
import { useGenerateInsightAndPerspective } from "@/features/account-plan/api/position-apis/insight-and-perspective/create-insight-and-perspective-generate";
import { Button } from "@/components/ui/button";
import { useStakeholderMappingList } from "@/features/account-plan/api/position-apis/stakeholder-mapping/get-stakeholder-mapping-list";
import { getAccountPlanTableName } from "@/features/account-plan/constants";

export const InsightsAndPerspectiveTable = () => {
  const [tableData, setTableData] = useState<APInsightAndPerspective[]>([]);
  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({});

  const { id } = useParams<{ id: string }>();
  const accountId = parseInt(id);

  const { stakeholderMappingList } = useStakeholderMappingList({
    accountId,
    params: {
      disable_pagination: true,
    },
  });
  const { insightAndPerspectiveList } = useInsightAndPerspectiveList({
    accountId,
    params: {
      disable_pagination: true,
    },
  });

  const updateInsightAndPerspective = useUpdateInsightAndPerspective({});
  const deleteInsightAndPerspective = useDeleteInsightAndPerspective({});
  const generateInsightAndPerspective = useGenerateInsightAndPerspective({});

  useEffect(() => {
    if (!insightAndPerspectiveList) return;

    const newTableData = insightAndPerspectiveList
      ?.map((v, idx) => ({
        idx,
        ...v,
      }))
      .sort((a, b) => a.id - b.id);

    setTableData(newTableData);
  }, [insightAndPerspectiveList]);

  const selectedRows = Object.keys(rowSelection)
    .filter((rowId) => rowSelection[rowId])
    .map((idx) => tableData[parseInt(idx)]);

  const onChangeData = useCallback(
    async (data: Partial<APInsightAndPerspective>, id: number) => {
      try {
        setTableData((prev) =>
          prev.map((v) => (v.id === id ? { ...v, ...data } : v))
        );

        const stakeholderId = data.ap_stakeholder_mapping_item?.id;

        await updateInsightAndPerspective.mutateAsync({
          accountId,
          id,
          data: {
            ap_stakeholder_mapping_item_id: stakeholderId,
          },
        });
      } catch (_) {
        toast("An unexpected error occured when modifying data");
      }
    },
    [accountId, updateInsightAndPerspective]
  );

  const onDeleteRows = async () => {
    try {
      const promises = [];

      setTableData(
        tableData.filter((row) => !selectedRows.find((v) => v.id === row.id))
      );

      setRowSelection({});

      promises.push(
        selectedRows.map(async (row) => {
          if (!!row?.id) {
            return deleteInsightAndPerspective.mutateAsync({
              id: row.id,
              accountId,
            });
          }
        })
      );

      await Promise.all(promises);
    } catch (_) {
      toast("An unexpected error occured when deleting rows");
    }
  };

  const onGenerateInsightAndPerspective = async () => {
    try {
      await generateInsightAndPerspective.mutateAsync({
        accountId,
      });

      toast("Successfully generated the insight and perspective");
    } catch (_) {
      toast("An error occured while generating the insights and perspective");
    }
  };

  const columns: ColumnDef<APInsightAndPerspective>[] = useMemo(
    () => [
      {
        accessorKey: "desc",
        header: "",
        size: 800,
        cell: ({ row, table }) => {
          const isPreview =
            (table.options.meta as DataTableMeta)?.isPreview ?? false;
          const rowData = row.original;

          return (
            <div className={isPreview ? "text-4xl" : "text-lg"}>
              {rowData.description}
            </div>
          );
        },
        meta: {
          tooltip:
            "A detailed explanation of the insight, providing clarity on its significance and relevance to the account’s strategy or objectives.",
          padding: true,
        },
      },
      {
        accessorKey: "target_name",
        header: "",
        size: 200,
        cell: ({ row, table }) => {
          const isPreview =
            (table.options.meta as DataTableMeta)?.isPreview ?? false;
          const rowData = row.original;

          return (
            <SelectCell
              className={isPreview ? "text-4xl" : "text-lg"}
              isPreview={isPreview}
              value={rowData.ap_stakeholder_mapping_item?.name}
              onChange={(target_name) => {
                const ap_stakeholder_mapping_item =
                  stakeholderMappingList?.find((v) => v.name === target_name);

                onChangeData({ ap_stakeholder_mapping_item }, rowData.id);
              }}
              options={
                stakeholderMappingList
                  ?.map((v) => ({
                    label: v.name ?? "",
                    value: v.name ?? "",
                  }))
                  .filter((v) => !!v.value) ?? []
              }
            />
          );
        },
        meta: {
          tooltip:
            "Specifies the individual, team, or organization that the insights or perspective are directed toward, helping tailor strategic actions.",
          padding: true,
        },
      },
    ],
    [onChangeData, stakeholderMappingList]
  );

  return (
    <AccountTable
      type={AccountPlanTableType.INSIGHT_AND_PERSPECTIVE}
      onGenerate={onGenerateInsightAndPerspective}
      isLoading={generateInsightAndPerspective.isPending}
      footer={
        <Button
          variant="destructive"
          disabled={selectedRows.length === 0}
          onClick={onDeleteRows}
        >
          Delete Row
        </Button>
      }
    >
      <DataTable
        columns={columns}
        data={tableData}
        rowSelection={rowSelection}
        setRowSelection={setRowSelection}
        showHeader={false}
        tableType={AccountPlanTableType.INSIGHT_AND_PERSPECTIVE}
        emptyMessage={
          <p className="mt-[10vh] text-3xl text-neutral-300">
            Generate analysis to view Insights and Perspective
          </p>
        }
      />
    </AccountTable>
  );
};
