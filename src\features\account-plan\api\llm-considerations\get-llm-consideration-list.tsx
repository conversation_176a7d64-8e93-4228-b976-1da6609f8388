import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, BaseParams, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import {
  LLMConsiderationData,
  LLMConsiderationUsage,
} from "../../types/llm-consideration-types";

type LLMConsiderationListParams = BaseParams & {
  usage_on: LLMConsiderationUsage;
};

export const getLLMConsiderationList = ({
  params,
}: {
  params?: LLMConsiderationListParams;
}): ApiResponse<LLMConsiderationData[]> => {
  return api.get(API_ROUTES.LLM_CONSIDERATIONS, { params });
};

export const getLLMConsiderationListQueryOptions = (
  params?: LLMConsiderationListParams
) => {
  return queryOptions({
    queryKey: [QUERY_KEYS.LLM_CONSIDERATIONS, params],
    queryFn: () => getLLMConsiderationList({ params }),
  });
};

type UseLLMConsiderationOptions = {
  params?: LLMConsiderationListParams;
  queryConfig?: QueryConfig<typeof getLLMConsiderationList>;
};

export const useLLMConsiderationList = ({
  queryConfig,
  params,
}: UseLLMConsiderationOptions) => {
  const llmConsiderationListQuery = useQuery({
    ...getLLMConsiderationListQueryOptions(params),
    ...queryConfig,
  });

  return {
    ...llmConsiderationListQuery,
    llmConsiderationList: llmConsiderationListQuery.data?.data || [],
  };
};
