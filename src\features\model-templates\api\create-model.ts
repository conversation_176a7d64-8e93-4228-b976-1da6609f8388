import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { ModelTemplate, ModelTemplateBaseData } from "../types";

export const createModel = ({
  data,
}: {
  data?: Partial<ModelTemplateBaseData>;
}): ApiResponse<ModelTemplate> => {
  return api.post(API_ROUTES.MODEL_TEMPLATES, data);
};

type UseCreateModelOptions = {
  mutationConfig?: MutationConfig<typeof createModel>;
};

export const useCreateModel = ({ mutationConfig }: UseCreateModelOptions) => {
  const { onSuccess, ...restConfig } = mutationConfig || {};
  const queryClient = useQueryClient();

  return useMutation({
    onSuccess: async (...args) => {
      onSuccess?.(...args);
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.MODEL_TEMPLATES],
      });
    },
    ...restConfig,
    mutationFn: createModel,
  });
};
