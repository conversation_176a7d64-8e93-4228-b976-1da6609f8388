import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import {
  APCircumstantialAnalysis,
  APCircumstantialAnalysisBaseData,
} from "@/features/account-plan/types/position-types";
import { QUERY_KEYS } from "@/constants/query-keys";

export const createCircumstantialAnalysis = ({
  accountId,
  data,
}: {
  accountId: number;
  data?: APCircumstantialAnalysisBaseData;
}): ApiResponse<APCircumstantialAnalysis> => {
  return api.post(
    API_ROUTES.ACCOUNT_PLANS_CIRCUMSTANTIAL_ANALYSIS(accountId),
    data
  );
};

type UseCreateCircumstantialAnalysisOptions = {
  mutationConfig?: MutationConfig<typeof createCircumstantialAnalysis>;
};

export const useCreateCircumstantialAnalysis = ({
  mutationConfig,
}: UseCreateCircumstantialAnalysisOptions) => {
  const queryClient = useQueryClient();
  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      await queryClient.invalidateQueries({
        queryKey: [
          QUERY_KEYS.ACCOUNT_PLANS,
          args[1].accountId,
          QUERY_KEYS.ACCOUNT_PLANS_CIRCUMSTANTIAL_ANALYSIS,
        ],
      });

      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: createCircumstantialAnalysis,
  });
};
